# 💳 Hướng dẫn Thanh toán Mo<PERSON>o Sandbox - Aurora Restaurant

## 🎯 Tổng quan

Hệ thống Aurora Restaurant đã được tích hợp đầy đủ chức năng thanh toán MoMo sandbox cho khách hàng. Khách hàng có thể thanh toán trực tiếp từ trang đặt món bằng MoMo hoặc tiền mặt.

## 🏗️ Kiến trúc Than<PERSON> toán

### 1. **Payment Service** (Port 3008)
- Xử lý tạo thanh toán MoMo
- Nhận IPN (Instant Payment Notification) từ MoMo
- Xử lý thanh toán tiền mặt
- Kiểm tra trạng thái thanh toán

### 2. **Table Service** (Port 3011)
- Giao diện thanh toán cho khách hàng
- Trang kết quả thanh toán
- Tích hợp với Payment Service

### 3. **API Gateway** (Port 3000)
- Routing các request thanh toán
- Proxy đến Payment Service

## 🔧 Cấu hình MoMo Sandbox

### Thông tin Test MoMo (API Keys chính thức):
```javascript
const MOMO_CONFIG = {
  partnerCode: 'MOMOBKUN20180529',
  accessKey: 'klm05TvNBzhg7h7j',
  secretKey: 'at67qH6mk8w5Y1nAyMoYKMWACiEi2bsa',
  endpoint: 'https://test-payment.momo.vn/v2/gateway/api/create',
  redirectUrl: 'http://***********:3011/payment/momo/return',
  ipnUrl: 'http://***********:3008/api/momo/ipn'
};
```

### Thông tin Test Account:
- **Tài khoản MoMo**: Bất kỳ tài khoản MoMo test nào
- **OTP Test**: 123456 (cho môi trường sandbox)
- **QR Code**: Quét bằng ứng dụng MoMo để thanh toán

## 🚀 Cách sử dụng

### 1. **Khởi động hệ thống**
```bash
npm start
```

### 2. **Truy cập trang đặt món**
- Quét QR code từ admin panel
- Hoặc truy cập: `http://localhost:3011/order?table_id=1&key=YOUR_KEY`

### 3. **Đặt món và thanh toán**
1. Chọn món ăn từ menu
2. Thêm vào giỏ hàng
3. Đặt món
4. **Chuyển sang tab "Đơn hàng"**
5. Xem trạng thái đơn hàng
6. **Nút "Thanh toán" sẽ hiển thị khi:**
   - Đơn hàng thường: Trạng thái "Đang chế biến" hoặc "Hoàn thành"
   - Đơn hàng buffet: Trạng thái "Đang phục vụ" hoặc "Hoàn thành"
7. Nhấn nút "Thanh toán"
8. Chọn phương thức thanh toán:
   - **MoMo**: QR Code hiển thị ngay trong modal
   - **Tiền mặt**: Nhập số tiền nhận

### 4. **Test thanh toán MoMo**
1. Chọn "MoMo" trong modal thanh toán
2. Nhấn "Thanh toán"
3. **QR Code sẽ hiển thị ngay trong modal**
4. Quét QR code bằng ứng dụng MoMo
5. Hoặc nhấn "Mở MoMo" để chuyển đến app
6. Nhập OTP test: 123456
7. Hoàn thành thanh toán
8. Hệ thống sẽ tự động cập nhật trạng thái

## 📱 Giao diện Thanh toán

### Modal Thanh toán
- Hiển thị thông tin đơn hàng
- Lựa chọn phương thức thanh toán
- Form nhập thông tin (tiền mặt)
- Nút xác nhận thanh toán

### Trang kết quả MoMo
- Hiển thị kết quả thanh toán
- Thông tin giao dịch
- Nút quay lại đơn hàng

## 🔄 Flow Thanh toán

### MoMo Flow:
1. **Khách hàng** chọn thanh toán MoMo
2. **Table Service** gửi request đến Payment Service
3. **Payment Service** tạo request đến MoMo API
4. **MoMo** trả về URL thanh toán
5. **Khách hàng** được chuyển đến trang MoMo
6. **Khách hàng** hoàn thành thanh toán trên MoMo
7. **MoMo** gửi IPN đến Payment Service
8. **Payment Service** cập nhật trạng thái đơn hàng
9. **Khách hàng** được chuyển về trang kết quả

### Cash Flow:
1. **Khách hàng** chọn thanh toán tiền mặt
2. **Nhân viên** nhập số tiền nhận
3. **Payment Service** tính tiền thừa
4. **Hệ thống** cập nhật trạng thái đã thanh toán

## 🧪 Test Scripts

### Test flow đặt món và thanh toán hoàn chỉnh:
```bash
node test_order_payment_flow.js
```

### Test demo QR Code MoMo:
```bash
node demo_momo_qr.js
```

### Kiểm tra đơn hàng hiện tại:
```bash
node test_order_payment_flow.js --check
```

Scripts sẽ:
- Tạo QR key cho bàn
- Tạo đơn hàng từ khách hàng
- Mô phỏng quá trình chế biến
- Test thanh toán MoMo QR Code
- Hiển thị link khách hàng để test

## 📊 API Endpoints

### Payment Service APIs:
- `POST /api/payments/momo/create` - Tạo thanh toán MoMo
- `POST /api/momo/ipn` - Nhận IPN từ MoMo
- `POST /api/payments/cash` - Thanh toán tiền mặt
- `GET /api/payments/status/:orderId` - Kiểm tra trạng thái

### Table Service APIs:
- `GET /payment/momo/return` - Trang kết quả MoMo
- Modal thanh toán trong trang đặt món

## 🔍 Debug & Troubleshooting

### 1. **Kiểm tra logs**
- Payment Service: Console logs cho MoMo requests/responses
- Table Service: Console logs cho payment actions

### 2. **Kiểm tra database**
- Bảng `payments`: Lưu thông tin thanh toán
- Bảng `orders`: Cập nhật `payment_status`

### 3. **Test MoMo sandbox**
- Đảm bảo sử dụng đúng thông tin test
- Kiểm tra network connectivity
- Verify IPN URL accessible

## 🎨 UI/UX Features

### Responsive Design
- Modal thanh toán responsive
- Tối ưu cho mobile
- Giao diện thân thiện

### Real-time Updates
- Kiểm tra trạng thái thanh toán tự động
- Cập nhật UI khi thanh toán thành công
- Thông báo real-time

### Error Handling
- Xử lý lỗi thanh toán
- Thông báo lỗi rõ ràng
- Retry mechanism

## 🔐 Security

### MoMo Security
- Signature verification
- Request validation
- Secure endpoints

### Data Protection
- Không lưu thông tin nhạy cảm
- Encrypt sensitive data
- Secure communication

## 📈 Monitoring

### Logs
- Payment requests/responses
- Error tracking
- Performance monitoring

### Analytics
- Payment success rate
- Popular payment methods
- Transaction volumes

---

**🎉 Chúc mừng! Hệ thống thanh toán MoMo sandbox đã sẵn sàng để test và sử dụng!**
