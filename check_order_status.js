const { db } = require('./shared');

async function checkOrder() {
  try {
    console.log('🔍 Checking order status for table 1...');
    
    const result = await db.executeQuery(`
      SELECT o.*, 
             COALESCE(o.payment_status, 'unpaid') as payment_status,
             COALESCE(o.is_buffet, 0) as is_buffet
      FROM orders o 
      WHERE o.table_id = 1 
        AND o.status NOT IN ('Đã thanh toán', 'Hoàn thành', 'Đ<PERSON> hoàn thành')
      ORDER BY o.order_time DESC
    `);
    
    console.log('📋 Current orders for table 1:');
    console.log(JSON.stringify(result.recordset, null, 2));
    
    if (result.recordset.length > 0) {
      const order = result.recordset[0];
      console.log('\n🔍 Payment button logic check:');
      console.log('- payment_status:', order.payment_status);
      console.log('- status:', order.status);
      console.log('- is_buffet:', order.is_buffet);
      console.log('- payment_status === "unpaid":', order.payment_status === 'unpaid');
      console.log('- status === "Hoàn thành":', order.status === 'Hoàn thành');
      console.log('- status === "Đang chế biến":', order.status === 'Đang chế biến');
      console.log('- status === "Đang phục vụ":', order.status === 'Đang phục vụ');
      console.log('- is_buffet && status === "Đang phục vụ":', order.is_buffet && order.status === 'Đang phục vụ');
      
      const showPaymentButton = order.payment_status === 'unpaid' &&
                               (order.status === 'Hoàn thành' ||
                                order.status === 'Đang chế biến' ||
                                (order.is_buffet && order.status === 'Đang phục vụ'));
      console.log('- showPaymentButton:', showPaymentButton);
      
      // Check order details
      const detailsResult = await db.executeQuery(`
        SELECT od.*,
               COALESCE(od.custom_name, f.name) as food_name,
               f.image_url as food_image
        FROM order_details od
        LEFT JOIN foods f ON od.food_id = f.id
        WHERE od.order_id = @orderId
      `, [{ name: 'orderId', type: db.sql.Int, value: order.id }]);
      
      console.log('\n📋 Order details:');
      console.log(JSON.stringify(detailsResult.recordset, null, 2));
    } else {
      console.log('❌ No active orders found for table 1');
    }
    
    process.exit(0);
  } catch (error) {
    console.error('Error:', error);
    process.exit(1);
  }
}

checkOrder();
