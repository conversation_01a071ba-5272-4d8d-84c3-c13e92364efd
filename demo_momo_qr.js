const axios = require('axios');

// <PERSON><PERSON><PERSON> hình
const API_BASE_URL = 'http://localhost:3000';
const TABLE_SERVICE_URL = 'http://localhost:3011';
const ADMIN_USERNAME = 'admin';
const ADMIN_PASSWORD = 'admin123';

// <PERSON><PERSON>u sắc cho console
const colors = {
    reset: '\x1b[0m',
    bright: '\x1b[1m',
    red: '\x1b[31m',
    green: '\x1b[32m',
    yellow: '\x1b[33m',
    blue: '\x1b[34m',
    magenta: '\x1b[35m',
    cyan: '\x1b[36m'
};

function log(message, color = 'reset') {
    console.log(`${colors[color]}${message}${colors.reset}`);
}

// Đăng nhập admin
async function loginAdmin() {
    try {
        const response = await axios.post(`${API_BASE_URL}/api/users/login`, {
            username: ADMIN_USERNAME,
            password: ADMIN_PASSWORD
        });
        return response.data.token;
    } catch (error) {
        throw new Error(`Lỗi đăng nhập: ${error.response?.data?.message || error.message}`);
    }
}

// Tạo QR key cho bàn
async function createTableKey(tableId, token) {
    try {
        const response = await axios.post(`${API_BASE_URL}/api/tables/${tableId}/qr`, {}, {
            headers: { 'Authorization': `Bearer ${token}` }
        });
        return response.data;
    } catch (error) {
        throw new Error(`Lỗi tạo QR key: ${error.response?.data?.message || error.message}`);
    }
}

// Tạo đơn hàng từ khách hàng
async function createCustomerOrder(tableId, tableKey) {
    try {
        const orderData = {
            table_id: tableId,
            table_key: tableKey,
            items: [
                { food_id: 1, quantity: 2 },
                { food_id: 2, quantity: 1 }
            ]
        };

        const response = await axios.post(`${TABLE_SERVICE_URL}/api/customer/orders`, orderData);
        return response.data;
    } catch (error) {
        throw new Error(`Lỗi tạo đơn hàng: ${error.response?.data?.message || error.message}`);
    }
}

// Cập nhật trạng thái đơn hàng thành "Hoàn thành"
async function completeOrder(orderId, token) {
    try {
        await axios.put(`${API_BASE_URL}/api/orders/${orderId}/status`, {
            status: 'Hoàn thành'
        }, {
            headers: { 'Authorization': `Bearer ${token}` }
        });
        log(`✅ Đã cập nhật đơn hàng #${orderId} thành "Hoàn thành"`, 'green');
    } catch (error) {
        log(`⚠️ Không thể cập nhật trạng thái đơn hàng: ${error.response?.data?.message || error.message}`, 'yellow');
    }
}

// Test tạo QR code MoMo
async function testMoMoQRCode(orderId, amount) {
    try {
        log('\n📱 Tạo QR Code MoMo...', 'yellow');
        
        const paymentData = {
            orderId: orderId,
            amount: amount,
            orderInfo: `Demo thanh toán QR đơn hàng #${orderId}`,
            tableId: 1
        };

        const response = await axios.post(`${API_BASE_URL}/api/payments/momo/create`, paymentData);
        
        if (response.data.success) {
            log('✅ Tạo QR Code MoMo thành công!', 'green');
            log(`🔗 URL thanh toán: ${response.data.payUrl}`, 'cyan');
            
            if (response.data.qrCodeUrl) {
                log(`📱 QR Code URL: ${response.data.qrCodeUrl}`, 'cyan');
                log('💡 Copy URL QR code vào trình duyệt để xem QR', 'blue');
            }
            
            if (response.data.deeplink) {
                log(`📲 Deep Link MoMo: ${response.data.deeplink}`, 'cyan');
            }
            
            return response.data;
        } else {
            throw new Error(response.data.message || 'Lỗi tạo QR Code MoMo');
        }
    } catch (error) {
        throw new Error(`Lỗi test QR MoMo: ${error.response?.data?.message || error.message}`);
    }
}

// Kiểm tra trạng thái thanh toán
async function checkPaymentStatus(orderId) {
    try {
        const response = await axios.get(`${API_BASE_URL}/api/payments/status/${orderId}`);
        log('📊 Trạng thái thanh toán:', 'blue');
        console.log(JSON.stringify(response.data, null, 2));
        return response.data;
    } catch (error) {
        throw new Error(`Lỗi kiểm tra trạng thái: ${error.response?.data?.message || error.message}`);
    }
}

// Demo QR Code MoMo
async function runMoMoQRDemo() {
    try {
        log('🚀 Demo QR Code MoMo Payment...', 'bright');
        log('=' .repeat(60), 'cyan');

        // 1. Đăng nhập admin
        log('\n1️⃣ Đăng nhập admin...', 'yellow');
        const token = await loginAdmin();
        log('✅ Đăng nhập thành công!', 'green');

        // 2. Tạo QR key cho bàn 1
        log('\n2️⃣ Tạo QR key cho bàn 1...', 'yellow');
        const qrData = await createTableKey(1, token);
        log(`✅ Tạo QR key thành công! Key: ${qrData.key}`, 'green');

        // 3. Tạo đơn hàng từ khách hàng
        log('\n3️⃣ Tạo đơn hàng từ khách hàng...', 'yellow');
        const order = await createCustomerOrder(1, qrData.key);
        log(`✅ Tạo đơn hàng thành công! ID: ${order.id}, Tổng: ${order.total.toLocaleString('vi-VN')} VNĐ`, 'green');

        // 4. Cập nhật trạng thái đơn hàng thành "Hoàn thành"
        log('\n4️⃣ Cập nhật trạng thái đơn hàng...', 'yellow');
        await completeOrder(order.id, token);

        // 5. Tạo QR Code MoMo
        log('\n5️⃣ Tạo QR Code MoMo...', 'yellow');
        const momoResult = await testMoMoQRCode(order.id, order.total);

        // 6. Hiển thị hướng dẫn
        log('\n📱 HƯỚNG DẪN SỬ DỤNG QR CODE MOMO:', 'magenta');
        log('=' .repeat(60), 'cyan');
        log('1. 📱 Mở ứng dụng MoMo trên điện thoại', 'cyan');
        log('2. 📷 Chọn "Quét QR" trong ứng dụng', 'cyan');
        log('3. 🔍 Quét QR code từ URL ở trên', 'cyan');
        log('4. 💰 Xác nhận thanh toán trong ứng dụng', 'cyan');
        log('5. 🔐 Nhập OTP test: 123456', 'cyan');
        log('6. ✅ Hoàn thành thanh toán', 'cyan');
        log('=' .repeat(60), 'cyan');

        // 7. Kiểm tra trạng thái ban đầu
        log('\n6️⃣ Kiểm tra trạng thái thanh toán ban đầu...', 'yellow');
        await checkPaymentStatus(order.id);

        // 8. Hướng dẫn tiếp theo
        log('\n🎯 BƯỚC TIẾP THEO:', 'bright');
        log('1. 📱 Quét QR code bằng ứng dụng MoMo', 'cyan');
        log('2. 🌐 Hoặc mở URL thanh toán trong trình duyệt', 'cyan');
        log(`3. 🔗 Hoặc truy cập trang khách hàng: ${TABLE_SERVICE_URL}/order?table_id=1&key=${qrData.key}`, 'cyan');
        log('4. 🔄 Chạy lại script này để kiểm tra trạng thái thanh toán', 'cyan');

        log('\n✅ Demo QR Code MoMo hoàn thành!', 'green');
        
        return {
            orderId: order.id,
            amount: order.total,
            tableKey: qrData.key,
            qrCodeUrl: momoResult.qrCodeUrl,
            payUrl: momoResult.payUrl,
            deeplink: momoResult.deeplink
        };

    } catch (error) {
        log(`💥 Lỗi trong quá trình demo: ${error.message}`, 'red');
        throw error;
    }
}

// Kiểm tra trạng thái thanh toán của đơn hàng gần nhất
async function checkLatestPayment() {
    try {
        log('🔍 Kiểm tra trạng thái thanh toán đơn hàng gần nhất...', 'bright');
        
        const token = await loginAdmin();
        
        // Lấy đơn hàng gần nhất
        const ordersResponse = await axios.get(`${API_BASE_URL}/api/orders`, {
            headers: { 'Authorization': `Bearer ${token}` }
        });
        
        if (ordersResponse.data.length === 0) {
            log('❌ Không tìm thấy đơn hàng nào', 'red');
            return;
        }

        const latestOrder = ordersResponse.data[0];
        log(`📋 Đơn hàng gần nhất: #${latestOrder.id} - ${latestOrder.total.toLocaleString('vi-VN')} VNĐ`, 'cyan');
        
        await checkPaymentStatus(latestOrder.id);
        
    } catch (error) {
        log(`💥 Lỗi kiểm tra thanh toán: ${error.message}`, 'red');
    }
}

// Main function
async function main() {
    const args = process.argv.slice(2);
    
    if (args.includes('--check')) {
        await checkLatestPayment();
    } else {
        await runMoMoQRDemo();
    }
}

// Chạy nếu file được gọi trực tiếp
if (require.main === module) {
    main().catch(error => {
        log(`💥 Lỗi: ${error.message}`, 'red');
        process.exit(1);
    });
}

module.exports = {
    runMoMoQRDemo,
    checkLatestPayment,
    testMoMoQRCode
};
