require('dotenv').config({ path: '../.env' });
const express = require('express');
const cors = require('cors');
const helmet = require('helmet');
const morgan = require('morgan');
const crypto = require('crypto');
const axios = require('axios');
const moment = require('moment');

// Import shared modules
const db = require('../shared/db');

const app = express();
const PORT = process.env.PAYMENT_SERVICE_PORT || 3008;

// Middleware
app.use(helmet());
app.use(cors());
app.use(morgan('combined'));
app.use(express.json());
app.use(express.urlencoded({ extended: true }));

// MoMo Test Configuration - Public Test Credentials
const MOMO_CONFIG = {
  partnerCode: 'MOMOBKUN20180529',
  accessKey: 'klm05TvNBzhg7h7j',
  secretKey: 'at67qH6mk8w5Y1nAyMoYKMWACiEi2bsa',
  endpoint: 'https://test-payment.momo.vn/v2/gateway/api/create',
  redirectUrl: process.env.MOMO_REDIRECT_URL || 'http://***********:3011/payment/momo/return',
  ipnUrl: process.env.MOMO_IPN_URL || 'http://***********:3008/api/momo/ipn'
};

// Utility functions
function generateSignature(rawData, secretKey) {
  return crypto.createHmac('sha256', secretKey).update(rawData).digest('hex');
}

function generateOrderId() {
  return 'ORDER_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
}

function generateRequestId() {
  return 'REQ_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
}

// Routes

// Lấy thông tin thanh toán của đơn hàng
app.get('/api/payments/order/:orderId', async (req, res) => {
  try {
    const { orderId } = req.params;
    
    const result = await db.executeQuery(`
      SELECT p.*, o.total as order_total, o.status as order_status, o.table_id
      FROM payments p
      INNER JOIN orders o ON p.order_id = o.id
      WHERE p.order_id = @orderId
      ORDER BY p.created_at DESC
    `, [
      { name: 'orderId', type: db.sql.Int, value: orderId }
    ]);

    res.json(result.recordset);
  } catch (error) {
    console.error('Error fetching payment info:', error);
    res.status(500).json({ message: 'Lỗi khi lấy thông tin thanh toán' });
  }
});

// Tạo thanh toán MoMo
app.post('/api/payments/momo/create', async (req, res) => {
  try {
    const { orderId, amount, orderInfo, tableId } = req.body;

    console.log('🔄 Tạo thanh toán MoMo:', { orderId, amount, orderInfo, tableId });

    // Kiểm tra đơn hàng tồn tại
    const orderResult = await db.executeQuery(`
      SELECT id, total, status, payment_status FROM orders WHERE id = @orderId
    `, [
      { name: 'orderId', type: db.sql.Int, value: orderId }
    ]);

    if (orderResult.recordset.length === 0) {
      return res.status(404).json({ message: 'Không tìm thấy đơn hàng' });
    }

    const order = orderResult.recordset[0];

    if (order.payment_status === 'paid') {
      return res.status(400).json({ message: 'Đơn hàng đã được thanh toán' });
    }

    // Kiểm tra số tiền khớp với đơn hàng
    if (amount !== order.total) {
      return res.status(400).json({ message: 'Số tiền thanh toán không khớp với đơn hàng' });
    }

    // Tạo MoMo payment request
    const momoOrderId = generateOrderId();
    const requestId = generateRequestId();
    const extraData = JSON.stringify({ tableId, orderId });
    
    const rawSignature = `accessKey=${MOMO_CONFIG.accessKey}&amount=${amount}&extraData=${extraData}&ipnUrl=${MOMO_CONFIG.ipnUrl}&orderId=${momoOrderId}&orderInfo=${orderInfo}&partnerCode=${MOMO_CONFIG.partnerCode}&redirectUrl=${MOMO_CONFIG.redirectUrl}&requestId=${requestId}&requestType=payWithATM`;
    
    const signature = generateSignature(rawSignature, MOMO_CONFIG.secretKey);

    const requestBody = {
      partnerCode: MOMO_CONFIG.partnerCode,
      partnerName: "Test",
      storeId: "MomoTestStore",
      requestId: requestId,
      amount: amount,
      orderId: momoOrderId,
      orderInfo: orderInfo,
      redirectUrl: MOMO_CONFIG.redirectUrl,
      ipnUrl: MOMO_CONFIG.ipnUrl,
      lang: 'vi',
      extraData: extraData,
      requestType: "captureWallet",
      signature: signature
    };

    console.log('📤 MoMo Request:', requestBody);

    // Gọi MoMo API
    const momoResponse = await axios.post(MOMO_CONFIG.endpoint, requestBody);

    console.log('📥 MoMo Response:', momoResponse.data);

    if (momoResponse.data.resultCode !== 0) {
      console.error('❌ MoMo API Error:', momoResponse.data);
      return res.status(400).json({
        message: 'Lỗi khi tạo thanh toán MoMo',
        error: momoResponse.data.message || 'Unknown error'
      });
    }

    if (momoResponse.data.resultCode === 0) {
      // Lưu thông tin thanh toán vào database
      await db.executeQuery(`
        INSERT INTO payments (order_id, payment_method, amount, status, momo_order_id, momo_request_id, momo_pay_url, momo_response_data)
        VALUES (@orderId, 'momo', @amount, 'pending', @momoOrderId, @requestId, @payUrl, @responseData)
      `, [
        { name: 'orderId', type: db.sql.Int, value: orderId },
        { name: 'amount', type: db.sql.Decimal, value: amount },
        { name: 'momoOrderId', type: db.sql.NVarChar, value: momoOrderId },
        { name: 'requestId', type: db.sql.NVarChar, value: requestId },
        { name: 'payUrl', type: db.sql.NVarChar, value: momoResponse.data.payUrl },
        { name: 'responseData', type: db.sql.NVarChar, value: JSON.stringify(momoResponse.data) }
      ]);

      // Cập nhật trạng thái thanh toán của đơn hàng
      await db.executeQuery(`
        UPDATE orders SET payment_status = 'pending' WHERE id = @orderId
      `, [
        { name: 'orderId', type: db.sql.Int, value: orderId }
      ]);

      res.json({
        success: true,
        payUrl: momoResponse.data.payUrl,
        qrCodeUrl: momoResponse.data.qrCodeUrl,
        deeplink: momoResponse.data.deeplink,
        deeplinkMiniApp: momoResponse.data.deeplinkMiniApp,
        momoOrderId: momoOrderId,
        requestId: requestId,
        amount: amount,
        message: 'Tạo thanh toán MoMo thành công'
      });
    } else {
      res.status(400).json({
        success: false,
        message: 'Lỗi từ MoMo: ' + momoResponse.data.message,
        errorCode: momoResponse.data.resultCode
      });
    }
  } catch (error) {
    console.error('Error creating MoMo payment:', error);
    res.status(500).json({ message: 'Lỗi khi tạo thanh toán MoMo' });
  }
});

// MoMo IPN (Instant Payment Notification)
app.post('/api/momo/ipn', async (req, res) => {
  try {
    console.log('MoMo IPN received:', req.body);
    
    const {
      partnerCode,
      orderId,
      requestId,
      amount,
      orderInfo,
      orderType,
      transId,
      resultCode,
      message,
      payType,
      responseTime,
      extraData,
      signature
    } = req.body;

    // Verify signature
    const rawSignature = `accessKey=${MOMO_CONFIG.accessKey}&amount=${amount}&extraData=${extraData}&message=${message}&orderId=${orderId}&orderInfo=${orderInfo}&orderType=${orderType}&partnerCode=${partnerCode}&payType=${payType}&requestId=${requestId}&responseTime=${responseTime}&resultCode=${resultCode}&transId=${transId}`;
    const expectedSignature = generateSignature(rawSignature, MOMO_CONFIG.secretKey);

    if (signature !== expectedSignature) {
      console.error('Invalid MoMo signature');
      return res.status(400).json({ message: 'Invalid signature' });
    }

    // Parse extraData để lấy orderId gốc
    let originalOrderId;
    try {
      const extra = JSON.parse(extraData);
      originalOrderId = extra.orderId;
    } catch (e) {
      console.error('Error parsing extraData:', e);
      return res.status(400).json({ message: 'Invalid extraData' });
    }

    // Cập nhật trạng thái thanh toán
    if (resultCode === 0) {
      // Thanh toán thành công
      await db.executeQuery(`
        UPDATE payments 
        SET status = 'completed', momo_trans_id = @transId, payment_time = GETDATE(), updated_at = GETDATE()
        WHERE momo_order_id = @momoOrderId
      `, [
        { name: 'transId', type: db.sql.NVarChar, value: transId },
        { name: 'momoOrderId', type: db.sql.NVarChar, value: orderId }
      ]);

      // Cập nhật trạng thái đơn hàng
      await db.executeQuery(`
        UPDATE orders 
        SET payment_status = 'paid', status = N'Đã thanh toán', updated_at = GETDATE()
        WHERE id = @orderId
      `, [
        { name: 'orderId', type: db.sql.Int, value: originalOrderId }
      ]);

      console.log(`Payment completed for order ${originalOrderId}`);
    } else {
      // Thanh toán thất bại
      await db.executeQuery(`
        UPDATE payments 
        SET status = 'failed', updated_at = GETDATE()
        WHERE momo_order_id = @momoOrderId
      `, [
        { name: 'momoOrderId', type: db.sql.NVarChar, value: orderId }
      ]);

      await db.executeQuery(`
        UPDATE orders 
        SET payment_status = 'unpaid'
        WHERE id = @orderId
      `, [
        { name: 'orderId', type: db.sql.Int, value: originalOrderId }
      ]);

      console.log(`Payment failed for order ${originalOrderId}: ${message}`);
    }

    res.status(204).send();
  } catch (error) {
    console.error('Error processing MoMo IPN:', error);
    res.status(500).json({ message: 'Error processing IPN' });
  }
});

// Thanh toán tiền mặt
app.post('/api/payments/cash', async (req, res) => {
  try {
    const { orderId, amount, cashReceived } = req.body;

    // Kiểm tra đơn hàng
    const orderResult = await db.executeQuery(`
      SELECT id, total, status, payment_status FROM orders WHERE id = @orderId
    `, [
      { name: 'orderId', type: db.sql.Int, value: orderId }
    ]);

    if (orderResult.recordset.length === 0) {
      return res.status(404).json({ message: 'Không tìm thấy đơn hàng' });
    }

    const order = orderResult.recordset[0];
    
    if (order.payment_status === 'paid') {
      return res.status(400).json({ message: 'Đơn hàng đã được thanh toán' });
    }

    if (cashReceived < amount) {
      return res.status(400).json({ message: 'Số tiền nhận không đủ' });
    }

    const cashChange = cashReceived - amount;

    // Lưu thông tin thanh toán
    await db.executeQuery(`
      INSERT INTO payments (order_id, payment_method, amount, status, cash_received, cash_change, payment_time)
      VALUES (@orderId, 'cash', @amount, 'completed', @cashReceived, @cashChange, GETDATE())
    `, [
      { name: 'orderId', type: db.sql.Int, value: orderId },
      { name: 'amount', type: db.sql.Decimal, value: amount },
      { name: 'cashReceived', type: db.sql.Decimal, value: cashReceived },
      { name: 'cashChange', type: db.sql.Decimal, value: cashChange }
    ]);

    // Cập nhật trạng thái đơn hàng
    await db.executeQuery(`
      UPDATE orders 
      SET payment_status = 'paid', status = N'Đã thanh toán', updated_at = GETDATE()
      WHERE id = @orderId
    `, [
      { name: 'orderId', type: db.sql.Int, value: orderId }
    ]);

    res.json({
      success: true,
      message: 'Thanh toán tiền mặt thành công',
      cashChange: cashChange
    });
  } catch (error) {
    console.error('Error processing cash payment:', error);
    res.status(500).json({ message: 'Lỗi khi xử lý thanh toán tiền mặt' });
  }
});

// Kiểm tra trạng thái thanh toán
app.get('/api/payments/status/:orderId', async (req, res) => {
  try {
    const { orderId } = req.params;
    
    const result = await db.executeQuery(`
      SELECT o.payment_status, o.status as order_status, p.status as payment_status_detail, 
             p.payment_method, p.amount, p.payment_time
      FROM orders o
      LEFT JOIN payments p ON o.id = p.order_id AND p.status = 'completed'
      WHERE o.id = @orderId
    `, [
      { name: 'orderId', type: db.sql.Int, value: orderId }
    ]);

    if (result.recordset.length === 0) {
      return res.status(404).json({ message: 'Không tìm thấy đơn hàng' });
    }

    res.json(result.recordset[0]);
  } catch (error) {
    console.error('Error checking payment status:', error);
    res.status(500).json({ message: 'Lỗi khi kiểm tra trạng thái thanh toán' });
  }
});

// Error handling
app.use((err, req, res, next) => {
  console.error(err.stack);
  res.status(500).json({ message: 'Đã xảy ra lỗi server' });
});

// Start server
app.listen(PORT, () => {
  console.log(`Payment Service đang chạy tại http://localhost:${PORT}`);
});

module.exports = app;
