/* <PERSON><PERSON><PERSON> chỉnh giao diện menu */

/* Ti<PERSON>u đề trang */
#menu h2 {
    font-size: 28px;
    color: #333;
    margin-bottom: 25px;
    border-bottom: 2px solid var(--primary-color);
    padding-bottom: 10px;
    position: relative;
}

#menu h2::after {
    content: '';
    position: absolute;
    bottom: -2px;
    left: 0;
    width: 80px;
    height: 2px;
    background-color: var(--primary-color);
}

/* <PERSON>h công cụ */
#menu .action-bar {
    background-color: #f9f9f9;
    padding: 15px;
    border-radius: 8px;
    margin-bottom: 25px;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.05);
    display: flex;
    flex-wrap: wrap;
    gap: 15px;
    align-items: center;
}

#menu .action-bar button {
    padding: 10px 18px;
    font-weight: 600;
    border-radius: 6px;
    transition: all 0.3s ease;
}

#menu .action-bar button:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

#menu .action-bar .filter {
    margin-left: auto;
}

#menu .action-bar select {
    padding: 10px 15px;
    border-radius: 6px;
    border: 1px solid #ddd;
    background-color: white;
    font-size: 14px;
    min-width: 180px;
    cursor: pointer;
    transition: all 0.3s ease;
}

#menu .action-bar select:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 2px rgba(76, 175, 80, 0.2);
    outline: none;
}

/* Lưới menu */
.menu-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
    gap: 25px;
    margin-top: 25px;
}

/* Card món ăn */
.food-card {
    background-color: white;
    border-radius: 12px;
    overflow: hidden;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.08);
    transition: all 0.3s ease;
    position: relative;
    border: 1px solid #eee;
}

.food-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.12);
}

.food-image {
    height: 180px;
    background-size: cover;
    background-position: center;
    position: relative;
    overflow: hidden;
}

.food-image::before {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    width: 100%;
    height: 50%;
    background: linear-gradient(to top, rgba(0, 0, 0, 0.6), transparent);
    opacity: 0;
    transition: opacity 0.3s ease;
}

.food-card:hover .food-image::before {
    opacity: 1;
}

.food-category-badge {
    position: absolute;
    top: 15px;
    right: 15px;
    background-color: rgba(76, 175, 80, 0.9);
    color: white;
    padding: 5px 12px;
    border-radius: 20px;
    font-size: 12px;
    font-weight: 600;
    z-index: 1;
}

.food-info {
    padding: 20px;
    position: relative;
}

.food-name {
    font-size: 18px;
    font-weight: 700;
    margin-bottom: 8px;
    color: #333;
}

.food-category {
    color: #666;
    font-size: 14px;
    margin-bottom: 12px;
    display: flex;
    align-items: center;
}

.food-category i {
    margin-right: 5px;
    color: var(--primary-color);
}

.food-price {
    font-weight: 700;
    color: var(--primary-color);
    font-size: 18px;
    margin-bottom: 15px;
    display: inline-block;
    background-color: rgba(76, 175, 80, 0.1);
    padding: 5px 12px;
    border-radius: 20px;
}

.food-actions {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-top: 10px;
}

.food-actions button {
    padding: 8px 15px;
    border-radius: 6px;
    font-size: 13px;
    font-weight: 600;
    transition: all 0.2s ease;
}

.food-actions button:hover {
    transform: translateY(-2px);
}

.food-actions .edit-btn {
    background-color: var(--secondary-color);
    color: white;
}

.food-actions .delete-btn {
    background-color: var(--danger-color);
    color: white;
}

/* Checkbox cho chọn nhiều */
.food-select {
    position: absolute;
    top: 15px;
    left: 15px;
    z-index: 2;
}

.food-select input[type="checkbox"] {
    width: 20px;
    height: 20px;
    cursor: pointer;
}

/* Trạng thái không có món ăn */
.empty-menu {
    grid-column: 1 / -1;
    text-align: center;
    padding: 50px 20px;
    background-color: #f9f9f9;
    border-radius: 8px;
    color: #666;
}

.empty-menu i {
    font-size: 48px;
    color: #ddd;
    margin-bottom: 15px;
    display: block;
}

/* Responsive */
@media (max-width: 768px) {
    .menu-grid {
        grid-template-columns: repeat(auto-fill, minmax(220px, 1fr));
    }
    
    #menu .action-bar {
        flex-direction: column;
        align-items: flex-start;
    }
    
    #menu .action-bar .filter {
        margin-left: 0;
        width: 100%;
    }
    
    #menu .action-bar select {
        width: 100%;
    }
}
