// API Configuration
const API_URL = 'http://localhost:3000/api';

// DOM Elements
const loginForm = document.getElementById('login-form');
const usernameInput = document.getElementById('username');
const passwordInput = document.getElementById('password');
const passwordToggle = document.getElementById('password-toggle');
const rememberMeCheckbox = document.getElementById('remember-me');
const loginBtn = document.getElementById('login-btn');
const errorMessage = document.getElementById('error-message');
const errorText = document.getElementById('error-text');
const notification = document.getElementById('notification');

// Initialize
document.addEventListener('DOMContentLoaded', function() {
    // Check if already logged in
    checkExistingLogin();
    
    // Setup event listeners
    setupEventListeners();
    
    // Load saved credentials if remember me was checked
    loadSavedCredentials();
});

function checkExistingLogin() {
    const token = localStorage.getItem('token');
    if (token) {
        // Verify token with server
        verifyToken(token);
    }
}

async function verifyToken(token) {
    try {
        const response = await fetch(`${API_URL}/users/me`, {
            headers: {
                'Authorization': `Bearer ${token}`
            }
        });

        if (response.ok) {
            // Token is valid, redirect to dashboard
            showNotification('Đã đăng nhập, đang chuyển hướng...', 'success');
            setTimeout(() => {
                window.location.href = 'index.html';
            }, 1000);
        } else {
            // Token is invalid, remove it
            localStorage.removeItem('token');
        }
    } catch (error) {
        console.error('Error verifying token:', error);
        localStorage.removeItem('token');
    }
}

function setupEventListeners() {
    // Login form submission
    loginForm.addEventListener('submit', handleLogin);
    
    // Password toggle
    passwordToggle.addEventListener('click', togglePasswordVisibility);
    
    // Input focus effects
    [usernameInput, passwordInput].forEach(input => {
        input.addEventListener('focus', () => {
            hideError();
        });
    });
    
    // Enter key handling
    document.addEventListener('keydown', (e) => {
        if (e.key === 'Enter' && !loginBtn.disabled) {
            handleLogin(e);
        }
    });
}

function loadSavedCredentials() {
    const savedUsername = localStorage.getItem('savedUsername');
    const rememberMe = localStorage.getItem('rememberMe') === 'true';
    
    if (rememberMe && savedUsername) {
        usernameInput.value = savedUsername;
        rememberMeCheckbox.checked = true;
        passwordInput.focus();
    } else {
        usernameInput.focus();
    }
}

function togglePasswordVisibility() {
    const isPassword = passwordInput.type === 'password';
    passwordInput.type = isPassword ? 'text' : 'password';
    
    const icon = passwordToggle.querySelector('i');
    icon.className = isPassword ? 'fas fa-eye-slash' : 'fas fa-eye';
}

async function handleLogin(event) {
    event.preventDefault();
    
    const username = usernameInput.value.trim();
    const password = passwordInput.value;
    
    // Validation
    if (!username || !password) {
        showError('Vui lòng nhập đầy đủ thông tin');
        return;
    }
    
    // Show loading state
    setLoadingState(true);
    hideError();
    
    try {
        const response = await fetch(`${API_URL}/users/login`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({ username, password })
        });
        
        if (!response.ok) {
            const errorData = await response.json();
            throw new Error(errorData.message || 'Đăng nhập thất bại');
        }
        
        const data = await response.json();
        
        // Save token
        localStorage.setItem('token', data.token);
        
        // Handle remember me
        if (rememberMeCheckbox.checked) {
            localStorage.setItem('savedUsername', username);
            localStorage.setItem('rememberMe', 'true');
        } else {
            localStorage.removeItem('savedUsername');
            localStorage.removeItem('rememberMe');
        }
        
        // Show success message
        showNotification('Đăng nhập thành công! Đang chuyển hướng...', 'success');
        
        // Redirect to dashboard
        setTimeout(() => {
            window.location.href = 'index.html';
        }, 1500);
        
    } catch (error) {
        console.error('Login error:', error);
        showError(error.message || 'Đăng nhập thất bại. Vui lòng thử lại.');
        setLoadingState(false);
    }
}

function setLoadingState(loading) {
    loginBtn.disabled = loading;
    
    const btnText = loginBtn.querySelector('.btn-text');
    const btnLoading = loginBtn.querySelector('.btn-loading');
    
    if (loading) {
        btnText.style.display = 'none';
        btnLoading.style.display = 'inline-flex';
    } else {
        btnText.style.display = 'inline';
        btnLoading.style.display = 'none';
    }
}

function showError(message) {
    errorText.textContent = message;
    errorMessage.style.display = 'flex';
    
    // Add shake animation
    errorMessage.style.animation = 'shake 0.5s ease-in-out';
    setTimeout(() => {
        errorMessage.style.animation = '';
    }, 500);
}

function hideError() {
    errorMessage.style.display = 'none';
}

function showNotification(message, type = 'info') {
    const notificationMessage = notification.querySelector('.notification-message');
    notificationMessage.textContent = message;
    
    // Remove existing type classes
    notification.classList.remove('success', 'error', 'info');
    notification.classList.add(type);
    
    // Show notification
    notification.classList.add('show');
    
    // Auto hide after 3 seconds
    setTimeout(() => {
        notification.classList.remove('show');
    }, 3000);
}

// Add shake animation CSS
const style = document.createElement('style');
style.textContent = `
    @keyframes shake {
        0%, 100% { transform: translateX(0); }
        25% { transform: translateX(-5px); }
        75% { transform: translateX(5px); }
    }
`;
document.head.appendChild(style);
