const axios = require('axios');

// <PERSON><PERSON><PERSON> hình
const API_BASE_URL = 'http://localhost:3000';
const TABLE_SERVICE_URL = 'http://localhost:3011';
const ADMIN_USERNAME = 'admin';
const ADMIN_PASSWORD = 'admin123';

// <PERSON><PERSON>u sắc cho console
const colors = {
    reset: '\x1b[0m',
    bright: '\x1b[1m',
    red: '\x1b[31m',
    green: '\x1b[32m',
    yellow: '\x1b[33m',
    blue: '\x1b[34m',
    magenta: '\x1b[35m',
    cyan: '\x1b[36m'
};

function log(message, color = 'reset') {
    console.log(`${colors[color]}${message}${colors.reset}`);
}

// Đăng nhập admin
async function loginAdmin() {
    try {
        const response = await axios.post(`${API_BASE_URL}/api/users/login`, {
            username: ADMIN_USERNAME,
            password: ADMIN_PASSWORD
        });
        return response.data.token;
    } catch (error) {
        throw new Error(`Lỗi đăng nhập: ${error.response?.data?.message || error.message}`);
    }
}

// Tạo QR key cho bàn
async function createTableKey(tableId, token) {
    try {
        const response = await axios.post(`${API_BASE_URL}/api/tables/${tableId}/qr`, {}, {
            headers: { 'Authorization': `Bearer ${token}` }
        });
        return response.data;
    } catch (error) {
        throw new Error(`Lỗi tạo QR key: ${error.response?.data?.message || error.message}`);
    }
}

// Tạo đơn hàng từ khách hàng
async function createCustomerOrder(tableId, tableKey) {
    try {
        const orderData = {
            table_id: tableId,
            table_key: tableKey,
            items: [
                { food_id: 1, quantity: 2 },
                { food_id: 2, quantity: 1 },
                { food_id: 3, quantity: 1 }
            ]
        };

        const response = await axios.post(`${TABLE_SERVICE_URL}/api/customer/orders`, orderData);
        return response.data;
    } catch (error) {
        throw new Error(`Lỗi tạo đơn hàng: ${error.response?.data?.message || error.message}`);
    }
}

// Kiểm tra đơn hàng của khách
async function checkCustomerOrder(tableId, tableKey) {
    try {
        const response = await axios.get(`${TABLE_SERVICE_URL}/api/customer/orders/${tableId}?key=${tableKey}`);
        return response.data;
    } catch (error) {
        throw new Error(`Lỗi kiểm tra đơn hàng: ${error.response?.data?.message || error.message}`);
    }
}

// Cập nhật trạng thái đơn hàng
async function updateOrderStatus(orderId, status, token) {
    try {
        await axios.put(`${API_BASE_URL}/api/orders/${orderId}/status`, {
            status: status
        }, {
            headers: { 'Authorization': `Bearer ${token}` }
        });
        log(`✅ Đã cập nhật đơn hàng #${orderId} thành "${status}"`, 'green');
    } catch (error) {
        log(`⚠️ Không thể cập nhật trạng thái đơn hàng: ${error.response?.data?.message || error.message}`, 'yellow');
    }
}

// Test flow đặt món và thanh toán hoàn chỉnh
async function runCompleteOrderPaymentFlow() {
    try {
        log('🚀 Test flow đặt món và thanh toán hoàn chỉnh...', 'bright');
        log('=' .repeat(70), 'cyan');

        // 1. Đăng nhập admin
        log('\n1️⃣ Đăng nhập admin...', 'yellow');
        const token = await loginAdmin();
        log('✅ Đăng nhập thành công!', 'green');

        // 2. Tạo QR key cho bàn 1
        log('\n2️⃣ Tạo QR key cho bàn 1...', 'yellow');
        const qrData = await createTableKey(1, token);
        log(`✅ Tạo QR key thành công! Key: ${qrData.key}`, 'green');
        log(`🔗 Link khách hàng: ${TABLE_SERVICE_URL}/order?table_id=1&key=${qrData.key}`, 'cyan');

        // 3. Khách hàng tạo đơn hàng
        log('\n3️⃣ Khách hàng tạo đơn hàng...', 'yellow');
        const order = await createCustomerOrder(1, qrData.key);
        log(`✅ Đơn hàng được tạo thành công!`, 'green');
        log(`📋 ID: ${order.id}`, 'cyan');
        log(`💰 Tổng tiền: ${order.total.toLocaleString('vi-VN')} VNĐ`, 'cyan');
        log(`📊 Trạng thái: ${order.status}`, 'cyan');
        log(`💳 Thanh toán: ${order.payment_status}`, 'cyan');

        // 4. Kiểm tra đơn hàng từ phía khách
        log('\n4️⃣ Kiểm tra đơn hàng từ phía khách...', 'yellow');
        let customerOrder = await checkCustomerOrder(1, qrData.key);
        log(`✅ Khách có thể xem đơn hàng #${customerOrder.id}`, 'green');
        
        // Kiểm tra nút thanh toán
        const canPayNow = customerOrder.payment_status === 'unpaid' &&
                         (customerOrder.status === 'Hoàn thành' ||
                          customerOrder.status === 'Đang chế biến' ||
                          (customerOrder.is_buffet && customerOrder.status === 'Đang phục vụ'));
        
        log(`🔍 Có thể thanh toán ngay: ${canPayNow ? 'CÓ' : 'KHÔNG'}`, canPayNow ? 'green' : 'yellow');
        
        if (canPayNow) {
            log('✅ Nút thanh toán sẽ hiển thị trong tab "Đơn hàng"', 'green');
        } else {
            log('⏳ Cần chờ đơn hàng hoàn thành mới có thể thanh toán', 'yellow');
        }

        // 5. Mô phỏng quá trình chế biến
        log('\n5️⃣ Mô phỏng quá trình chế biến...', 'yellow');
        
        // Chuyển sang "Đang chế biến"
        await updateOrderStatus(order.id, 'Đang chế biến', token);
        customerOrder = await checkCustomerOrder(1, qrData.key);
        
        const canPayAfterCooking = customerOrder.payment_status === 'unpaid' &&
                                  (customerOrder.status === 'Hoàn thành' ||
                                   customerOrder.status === 'Đang chế biến');
        
        log(`🔍 Có thể thanh toán khi đang chế biến: ${canPayAfterCooking ? 'CÓ' : 'KHÔNG'}`, canPayAfterCooking ? 'green' : 'yellow');

        // Chuyển sang "Hoàn thành"
        await updateOrderStatus(order.id, 'Hoàn thành', token);
        customerOrder = await checkCustomerOrder(1, qrData.key);
        
        const canPayAfterComplete = customerOrder.payment_status === 'unpaid' &&
                                   customerOrder.status === 'Hoàn thành';
        
        log(`🔍 Có thể thanh toán khi hoàn thành: ${canPayAfterComplete ? 'CÓ' : 'KHÔNG'}`, canPayAfterComplete ? 'green' : 'yellow');

        // 6. Hướng dẫn test thanh toán
        log('\n6️⃣ Hướng dẫn test thanh toán...', 'yellow');
        log('📱 CÁCH TEST THANH TOÁN:', 'magenta');
        log('=' .repeat(50), 'cyan');
        log(`1. 🌐 Mở link: ${TABLE_SERVICE_URL}/order?table_id=1&key=${qrData.key}`, 'cyan');
        log('2. 📋 Chuyển sang tab "Đơn hàng"', 'cyan');
        log('3. 💳 Nhấn nút "Thanh toán" (màu xanh gradient)', 'cyan');
        log('4. 📱 Chọn "MoMo" để test QR code', 'cyan');
        log('5. 💵 Hoặc chọn "Tiền mặt" để test thanh toán tiền mặt', 'cyan');
        log('=' .repeat(50), 'cyan');

        // 7. Test tạo QR MoMo
        log('\n7️⃣ Test tạo QR Code MoMo...', 'yellow');
        try {
            const paymentData = {
                orderId: order.id,
                amount: order.total,
                orderInfo: `Test thanh toán đơn hàng #${order.id}`,
                tableId: 1
            };

            const response = await axios.post(`${API_BASE_URL}/api/payments/momo/create`, paymentData);
            
            if (response.data.success) {
                log('✅ Tạo QR Code MoMo thành công!', 'green');
                log(`🔗 URL thanh toán: ${response.data.payUrl}`, 'cyan');
                if (response.data.qrCodeUrl) {
                    log(`📱 QR Code URL: ${response.data.qrCodeUrl}`, 'cyan');
                }
            }
        } catch (error) {
            log(`⚠️ Lỗi tạo QR MoMo: ${error.response?.data?.message || error.message}`, 'yellow');
        }

        // 8. Kết quả
        log('\n✅ Test flow hoàn thành!', 'green');
        log('\n🎯 KẾT QUẢ:', 'bright');
        log(`📋 Đơn hàng #${order.id} đã sẵn sàng thanh toán`, 'green');
        log(`💰 Tổng tiền: ${order.total.toLocaleString('vi-VN')} VNĐ`, 'green');
        log(`🔗 Link khách hàng: ${TABLE_SERVICE_URL}/order?table_id=1&key=${qrData.key}`, 'green');
        log('💳 Nút thanh toán sẽ hiển thị trong tab "Đơn hàng"', 'green');

        return {
            orderId: order.id,
            amount: order.total,
            tableKey: qrData.key,
            customerUrl: `${TABLE_SERVICE_URL}/order?table_id=1&key=${qrData.key}`
        };

    } catch (error) {
        log(`💥 Lỗi trong quá trình test: ${error.message}`, 'red');
        throw error;
    }
}

// Kiểm tra trạng thái đơn hàng hiện tại
async function checkCurrentOrders() {
    try {
        log('🔍 Kiểm tra đơn hàng hiện tại...', 'bright');
        
        const token = await loginAdmin();
        
        const ordersResponse = await axios.get(`${API_BASE_URL}/api/orders`, {
            headers: { 'Authorization': `Bearer ${token}` }
        });
        
        if (ordersResponse.data.length === 0) {
            log('❌ Không có đơn hàng nào', 'red');
            return;
        }

        log(`📊 Tìm thấy ${ordersResponse.data.length} đơn hàng:`, 'cyan');
        
        ordersResponse.data.slice(0, 5).forEach((order, index) => {
            log(`${index + 1}. Đơn #${order.id} - ${order.total.toLocaleString('vi-VN')} VNĐ - ${order.status} - ${order.payment_status}`, 'cyan');
        });
        
    } catch (error) {
        log(`💥 Lỗi kiểm tra đơn hàng: ${error.message}`, 'red');
    }
}

// Main function
async function main() {
    const args = process.argv.slice(2);
    
    if (args.includes('--check')) {
        await checkCurrentOrders();
    } else {
        await runCompleteOrderPaymentFlow();
    }
}

// Chạy nếu file được gọi trực tiếp
if (require.main === module) {
    main().catch(error => {
        log(`💥 Lỗi: ${error.message}`, 'red');
        process.exit(1);
    });
}

module.exports = {
    runCompleteOrderPaymentFlow,
    checkCurrentOrders
};
