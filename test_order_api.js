// Test script to check order API directly
const http = require('http');

function testOrderAPI() {
    return new Promise((resolve, reject) => {
        console.log('🔍 Testing order API...');

        const options = {
            hostname: 'localhost',
            port: 3011,
            path: '/api/customer/orders/1?key=826ce96c-642f-4eed-a1b6-0c4570968bc2',
            method: 'GET'
        };

        const req = http.request(options, (res) => {
            let data = '';

            res.on('data', (chunk) => {
                data += chunk;
            });

            res.on('end', () => {
                try {
                    if (res.statusCode !== 200) {
                        console.error('❌ API Error:', res.statusCode, res.statusMessage);
                        console.error('Response:', data);
                        reject(new Error(`API Error: ${res.statusCode}`));
                        return;
                    }

                    const order = JSON.parse(data);

                    console.log('✅ Order data received:');
                    console.log('- Order ID:', order.id);
                    console.log('- Status:', order.status);
                    console.log('- Payment Status:', order.payment_status);
                    console.log('- Is Buffet:', order.is_buffet);
                    console.log('- Total:', order.total);
                    console.log('- Details count:', order.details?.length || 0);

                    // Test payment button logic
                    const showPaymentButton = order.payment_status === 'unpaid' &&
                                             (order.status === 'Hoàn thành' ||
                                              order.status === 'Đang chế biến' ||
                                              order.status === 'Đang phục vụ');

                    console.log('\n🔍 Payment button logic test:');
                    console.log('- payment_status === "unpaid":', order.payment_status === 'unpaid');
                    console.log('- status === "Hoàn thành":', order.status === 'Hoàn thành');
                    console.log('- status === "Đang chế biến":', order.status === 'Đang chế biến');
                    console.log('- status === "Đang phục vụ":', order.status === 'Đang phục vụ');
                    console.log('- showPaymentButton:', showPaymentButton);

                    if (order.details && order.details.length > 0) {
                        console.log('\n📋 Order details:');
                        order.details.forEach((item, index) => {
                            console.log(`  ${index + 1}. ${item.food_name} x${item.quantity} - ${item.price * item.quantity}đ`);
                        });
                    }

                    resolve(order);
                } catch (parseError) {
                    console.error('❌ Error parsing JSON:', parseError);
                    console.error('Raw response:', data);
                    reject(parseError);
                }
            });
        });

        req.on('error', (error) => {
            console.error('❌ Request error:', error);
            reject(error);
        });

        req.end();
    });
}

testOrderAPI().catch(console.error);
