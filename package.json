{"name": "restaurant-server", "version": "1.0.0", "description": "Restaurant Microservice System", "main": "index.js", "scripts": {"start": "concurrently \"npm run api-gateway\" \"npm run table-service\" \"npm run menu-service\" \"npm run order-service\" \"npm run kitchen-service\" \"npm run user-service\" \"npm run inventory-service\" \"npm run image-service\" \"npm run payment-service\"", "api-gateway": "cd api-gateway && npm start", "table-service": "cd table-service && npm start", "menu-service": "cd menu-service && npm start", "order-service": "cd order-service && npm start", "kitchen-service": "cd kitchen-service && npm start", "user-service": "cd user-service && npm start", "inventory-service": "cd inventory-service && npm start", "image-service": "cd image-service && npm start", "payment-service": "cd payment-service && npm start", "setup": "npm install && concurrently \"cd api-gateway && npm install\" \"cd table-service && npm install\" \"cd menu-service && npm install\" \"cd order-service && npm install\" \"cd kitchen-service && npm install\" \"cd user-service && npm install\" \"cd inventory-service && npm install\" \"cd image-service && npm install\" \"cd payment-service && npm install\" \"cd shared && npm install\""}, "keywords": ["restaurant", "microservice", "qrcode"], "author": "", "license": "ISC", "dependencies": {"concurrently": "^8.2.2", "dotenv": "^16.4.5", "mssql": "^11.0.1", "node-fetch": "^3.3.2"}}