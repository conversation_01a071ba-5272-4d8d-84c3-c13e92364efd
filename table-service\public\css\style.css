/* Reset và Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: #333;
    line-height: 1.6;
    min-height: 100vh;
    overflow-x: hidden;
}

.container {
    max-width: 100%;
    margin: 0 auto;
    padding: 0 16px;
}

/* Header */
.header {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(20px);
    color: #333;
    padding: 16px 0;
    box-shadow: 0 4px 30px rgba(0, 0, 0, 0.1);
    border-bottom: 1px solid rgba(255, 255, 255, 0.2);
}

.header .container {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.header h1 {
    font-size: 1.4rem;
    font-weight: 700;
    background: linear-gradient(135deg, #667eea, #764ba2);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.table-info {
    background: linear-gradient(135deg, #667eea, #764ba2);
    color: white;
    padding: 8px 16px;
    border-radius: 20px;
    font-weight: 600;
    font-size: 0.9rem;
    box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
}

/* Restaurant Introduction */
.restaurant-intro {
    color: white;
    padding: 2rem 0;
    position: relative;
    overflow: hidden;
    min-height: 300px;
}

/* Background Slideshow Container */
.restaurant-intro::before {
    content: '';
    position: absolute;
    top: -10%;
    left: -10%;
    width: 120%;
    height: 120%;
    z-index: -2;
    background-size: cover;
    background-position: center;
    background-repeat: no-repeat;
    animation: backgroundSlideshow 24s infinite, backgroundZoom 24s infinite;
    transition: opacity 1s ease-in-out;
}

/* Aurora Gradient Overlay */
.restaurant-intro::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: -1;
    background: linear-gradient(135deg, rgba(102, 126, 234, 0.75), rgba(118, 75, 162, 0.75));
}

/* Background Slideshow Animation */
@keyframes backgroundSlideshow {
    0%, 22% {
        background-image: url('https://images.unsplash.com/photo-1517248135467-4c7edcad34c4?ixlib=rb-4.0.3&auto=format&fit=crop&w=1920&q=80');
        opacity: 1;
    }
    25%, 47% {
        background-image: url('https://images.unsplash.com/photo-1414235077428-338989a2e8c0?ixlib=rb-4.0.3&auto=format&fit=crop&w=1920&q=80');
        opacity: 1;
    }
    50%, 72% {
        background-image: url('https://images.unsplash.com/photo-1551218808-94e220e084d2?ixlib=rb-4.0.3&auto=format&fit=crop&w=1920&q=80');
        opacity: 1;
    }
    75%, 97% {
        background-image: url('https://images.unsplash.com/photo-1559329007-40df8a9345d8?ixlib=rb-4.0.3&auto=format&fit=crop&w=1920&q=80');
        opacity: 1;
    }
    100% {
        background-image: url('https://images.unsplash.com/photo-1517248135467-4c7edcad34c4?ixlib=rb-4.0.3&auto=format&fit=crop&w=1920&q=80');
        opacity: 1;
    }
}

/* Background Zoom Animation for Parallax Effect */
@keyframes backgroundZoom {
    0%, 100% {
        transform: scale(1);
    }
    50% {
        transform: scale(1.05);
    }
}

/* Scroll to Top Button */
.scroll-to-top {
    position: fixed;
    bottom: 30px;
    right: 30px;
    width: 60px;
    height: 60px;
    background: linear-gradient(135deg, #667eea, #764ba2);
    border: none;
    border-radius: 50%;
    color: white;
    font-size: 24px;
    cursor: pointer;
    box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    z-index: 1000;
    opacity: 0;
    visibility: hidden;
    transform: translateY(20px);
    backdrop-filter: blur(10px);
    border: 2px solid rgba(255, 255, 255, 0.2);
    display: flex;
    align-items: center;
    justify-content: center;
}

.scroll-to-top.show {
    opacity: 1;
    visibility: visible;
    transform: translateY(0);
}

.scroll-to-top:hover {
    transform: translateY(-5px) scale(1.1);
    box-shadow: 0 15px 40px rgba(102, 126, 234, 0.4);
}

.scroll-to-top:active {
    transform: translateY(-3px) scale(1.05);
}

.scroll-to-top::before {
    content: '';
    position: absolute;
    top: -2px;
    left: -2px;
    right: -2px;
    bottom: -2px;
    background: linear-gradient(135deg, #667eea, #764ba2);
    border-radius: 50%;
    z-index: -1;
    opacity: 0;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.scroll-to-top:hover::before {
    opacity: 0.3;
    transform: scale(1.2);
}

/* Animation for scroll to top button */
@keyframes scrollButtonPulse {
    0%, 100% {
        box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
    }
    50% {
        box-shadow: 0 8px 25px rgba(102, 126, 234, 0.6);
    }
}

.scroll-to-top.pulse {
    animation: scrollButtonPulse 2s ease-in-out infinite;
}

/* Responsive adjustments for scroll to top button */
@media (max-width: 768px) {
    .scroll-to-top {
        bottom: 20px;
        right: 20px;
        width: 50px;
        height: 50px;
        font-size: 20px;
    }
}

@media (max-width: 480px) {
    .scroll-to-top {
        bottom: 15px;
        right: 15px;
        width: 45px;
        height: 45px;
        font-size: 18px;
    }
}



.intro-content {
    text-align: center;
    position: relative;
    z-index: 2;
    max-width: 600px;
    margin: 0 auto;
}

.intro-icon {
    width: 80px;
    height: 80px;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.2);
    backdrop-filter: blur(10px);
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 1.5rem;
    font-size: 2rem;
    color: white;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.2);
    animation: float 3s ease-in-out infinite;
}

@keyframes float {
    0%, 100% { transform: translateY(0px); }
    50% { transform: translateY(-10px); }
}

.intro-content h2 {
    font-size: 2.2rem;
    font-weight: 700;
    margin-bottom: 1rem;
    color: #ffffff;
    text-shadow: 0 2px 8px rgba(0, 0, 0, 0.5), 0 4px 16px rgba(0, 0, 0, 0.3);
    letter-spacing: 0.5px;
}

.intro-description {
    font-size: 1.1rem;
    line-height: 1.6;
    margin-bottom: 2rem;
    color: #ffffff;
    text-shadow: 0 1px 4px rgba(0, 0, 0, 0.5);
    font-weight: 400;
    max-width: 500px;
    margin-left: auto;
    margin-right: auto;
}

.intro-features {
    display: flex;
    justify-content: center;
    gap: 2rem;
    flex-wrap: wrap;
}

.feature-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 0.5rem;
    padding: 1.25rem;
    background: rgba(255, 255, 255, 0.15);
    backdrop-filter: blur(15px);
    border-radius: 20px;
    border: 1px solid rgba(255, 255, 255, 0.3);
    transition: all 0.3s ease;
    min-width: 120px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.2);
}

.feature-item:hover {
    transform: translateY(-8px);
    background: rgba(255, 255, 255, 0.25);
    box-shadow: 0 12px 35px rgba(0, 0, 0, 0.3);
    border-color: rgba(255, 255, 255, 0.4);
}

.feature-item i {
    font-size: 1.8rem;
    color: #ffffff;
    margin-bottom: 0.25rem;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.feature-item span {
    font-size: 0.95rem;
    font-weight: 600;
    color: #ffffff;
    text-align: center;
    text-shadow: 0 1px 3px rgba(0, 0, 0, 0.3);
}

/* Responsive Design for Restaurant Intro */
@media (max-width: 768px) {
    .restaurant-intro {
        padding: 1.5rem 0;
    }

    .intro-content h2 {
        font-size: 1.8rem;
    }

    .intro-description {
        font-size: 1rem;
        margin-bottom: 1.5rem;
    }

    .intro-features {
        gap: 1rem;
    }

    .feature-item {
        min-width: 80px;
        padding: 0.75rem;
    }

    .feature-item i {
        font-size: 1.2rem;
    }

    .feature-item span {
        font-size: 0.8rem;
    }
}

@media (max-width: 480px) {
    .restaurant-intro {
        padding: 1rem 0;
    }

    .intro-icon {
        width: 60px;
        height: 60px;
        font-size: 1.5rem;
        margin-bottom: 1rem;
    }

    .intro-content h2 {
        font-size: 1.5rem;
        margin-bottom: 0.75rem;
    }

    .intro-description {
        font-size: 0.9rem;
        margin-bottom: 1rem;
    }

    .intro-features {
        gap: 0.75rem;
    }

    .feature-item {
        min-width: 70px;
        padding: 0.5rem;
    }
}

/* Navigation Tabs */
.nav-tabs {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(20px);
    border-bottom: 1px solid rgba(0, 0, 0, 0.1);
    position: sticky;
    top: 0;
    z-index: 999;
    box-shadow: 0 2px 20px rgba(0, 0, 0, 0.1);
}

.nav-tabs .container {
    display: flex;
    justify-content: space-around;
    padding: 0;
}

.tab-btn {
    background: none;
    border: none;
    padding: 16px 12px;
    cursor: pointer;
    font-size: 0.85rem;
    color: #666;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
    flex: 1;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    gap: 4px;
    font-weight: 500;
    min-height: 60px;
}

.tab-btn i {
    font-size: 1.2rem;
    margin-bottom: 2px;
}

.tab-btn.active {
    color: #667eea;
    background: linear-gradient(135deg, rgba(102, 126, 234, 0.1), rgba(118, 75, 162, 0.1));
}

.tab-btn.active::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 50%;
    transform: translateX(-50%);
    width: 30px;
    height: 3px;
    background: linear-gradient(135deg, #667eea, #764ba2);
    border-radius: 2px;
}

.cart-count {
    background: linear-gradient(135deg, #ff6b6b, #ee5a24);
    color: white;
    border-radius: 50%;
    padding: 2px 6px;
    font-size: 0.7rem;
    min-width: 18px;
    height: 18px;
    display: flex;
    align-items: center;
    justify-content: center;
    position: absolute;
    top: 8px;
    right: 50%;
    transform: translateX(50%);
    font-weight: 600;
    box-shadow: 0 2px 8px rgba(255, 107, 107, 0.4);
}

/* Loading */
.loading {
    text-align: center;
    padding: 60px 20px;
    display: none;
}

.loading.show {
    display: block;
}

.spinner {
    border: 3px solid rgba(102, 126, 234, 0.1);
    border-top: 3px solid #667eea;
    border-radius: 50%;
    width: 40px;
    height: 40px;
    animation: spin 1s linear infinite;
    margin: 0 auto 16px;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Tab Content */
.tab-content {
    display: none;
    min-height: calc(100vh - 134px);
    padding: 20px 0 100px;
}

.tab-content.active {
    display: block;
}

/* Search Box */
.search-box {
    position: relative;
    margin-bottom: 20px;
}

.search-box i {
    position: absolute;
    left: 16px;
    top: 50%;
    transform: translateY(-50%);
    color: #999;
    font-size: 1.1rem;
}

.search-box input {
    width: 100%;
    padding: 16px 16px 16px 48px;
    border: none;
    border-radius: 16px;
    font-size: 1rem;
    background: rgba(255, 255, 255, 0.9);
    backdrop-filter: blur(10px);
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
}

.search-box input:focus {
    outline: none;
    background: white;
    box-shadow: 0 8px 30px rgba(102, 126, 234, 0.2);
    transform: translateY(-2px);
}

/* Menu Categories */
.menu-categories {
    display: block !important;
    width: 100%;
    min-height: 50px;
}

.category {
    margin-bottom: 24px;
    background: rgba(255, 255, 255, 0.9);
    backdrop-filter: blur(20px);
    border-radius: 20px;
    overflow: hidden;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.category-header {
    background: linear-gradient(135deg, #667eea, #764ba2);
    color: white;
    padding: 16px 20px;
    font-size: 1.1rem;
    font-weight: 700;
    display: flex;
    align-items: center;
    gap: 8px;
}

.category-header i {
    font-size: 1.2rem;
}

.foods-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(160px, 1fr));
    gap: 16px;
    padding: 20px;
}

.food-item {
    background: white;
    border-radius: 16px;
    overflow: hidden;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    cursor: pointer;
    border: 1px solid rgba(0, 0, 0, 0.05);
}

.food-item:hover {
    transform: translateY(-4px);
    box-shadow: 0 12px 40px rgba(102, 126, 234, 0.15);
}

.food-item:active {
    transform: translateY(-2px);
}

.food-image-container {
    position: relative;
    width: 100%;
    height: 120px;
    overflow: hidden;
    background: linear-gradient(135deg, #f8f9fa, #e9ecef);
}

.food-image {
    width: 100%;
    height: 100%;
    object-fit: cover;
    display: block;
    transition: transform 0.3s ease;
}

.food-item:hover .food-image {
    transform: scale(1.05);
}

.no-image-placeholder {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    background: linear-gradient(135deg, #f8f9fa, #e9ecef);
    color: #adb5bd;
    font-size: 12px;
}

.no-image-placeholder i {
    font-size: 20px;
    margin-bottom: 4px;
}

.food-info {
    padding: 12px;
}

.food-name {
    font-size: 0.9rem;
    font-weight: 600;
    margin-bottom: 6px;
    color: #333;
    line-height: 1.3;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
}

.food-price {
    color: #667eea;
    font-size: 1rem;
    font-weight: 700;
}

/* Cart */
.cart-items {
    margin-bottom: 20px;
}

.empty-cart {
    text-align: center;
    padding: 60px 20px;
    color: #adb5bd;
    background: rgba(255, 255, 255, 0.9);
    backdrop-filter: blur(20px);
    border-radius: 20px;
    margin: 20px 0;
}

.empty-cart i {
    font-size: 3rem;
    margin-bottom: 16px;
    color: #dee2e6;
}

.cart-item {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(20px);
    border-radius: 16px;
    padding: 16px;
    margin-bottom: 12px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
    display: flex;
    align-items: center;
    gap: 12px;
    border: 1px solid rgba(255, 255, 255, 0.2);
    transition: all 0.3s ease;
}

.cart-item:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 30px rgba(102, 126, 234, 0.15);
}

.cart-item-image-container {
    position: relative;
    width: 50px;
    height: 50px;
    border-radius: 12px;
    overflow: hidden;
    background: linear-gradient(135deg, #f8f9fa, #e9ecef);
    flex-shrink: 0;
}

.cart-item-image {
    width: 100%;
    height: 100%;
    object-fit: cover;
    display: block;
}

.cart-no-image-placeholder {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    background: linear-gradient(135deg, #f8f9fa, #e9ecef);
    color: #adb5bd;
    font-size: 10px;
}

.cart-no-image-placeholder i {
    font-size: 14px;
}

.cart-item-info {
    flex: 1;
    min-width: 0;
}

.cart-item-name {
    font-weight: 600;
    margin-bottom: 4px;
    font-size: 0.9rem;
    color: #333;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.cart-item-price {
    color: #667eea;
    font-weight: 600;
    font-size: 0.85rem;
}

.cart-item-controls {
    display: flex;
    align-items: center;
    gap: 8px;
    flex-shrink: 0;
}

.qty-btn {
    background: linear-gradient(135deg, #667eea, #764ba2);
    color: white;
    border: none;
    width: 28px;
    height: 28px;
    border-radius: 50%;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 0.8rem;
    font-weight: 600;
    transition: all 0.3s ease;
    box-shadow: 0 2px 8px rgba(102, 126, 234, 0.3);
}

.qty-btn:hover {
    transform: scale(1.1);
    box-shadow: 0 4px 12px rgba(102, 126, 234, 0.4);
}

.qty-btn:active {
    transform: scale(0.95);
}

.cart-item-quantity {
    min-width: 24px;
    text-align: center;
    font-weight: 700;
    font-size: 0.9rem;
    color: #333;
}

.remove-item {
    background: linear-gradient(135deg, #ff6b6b, #ee5a24);
    color: white;
    border: none;
    padding: 6px;
    border-radius: 8px;
    cursor: pointer;
    font-size: 0.8rem;
    transition: all 0.3s ease;
    box-shadow: 0 2px 8px rgba(255, 107, 107, 0.3);
}

.remove-item:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(255, 107, 107, 0.4);
}

/* Cart Summary */
.cart-summary {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(20px);
    border-radius: 20px;
    padding: 20px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
    position: sticky;
    bottom: 20px;
    border: 1px solid rgba(255, 255, 255, 0.2);
    margin: 0 -4px;
}

.total-row {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 16px;
    font-size: 1.1rem;
    font-weight: 600;
    color: #333;
}

.total-amount {
    color: #667eea;
    font-size: 1.4rem;
    font-weight: 700;
}

.order-btn {
    width: 100%;
    background: linear-gradient(135deg, #667eea, #764ba2);
    color: white;
    border: none;
    padding: 16px;
    border-radius: 16px;
    font-size: 1.1rem;
    font-weight: 700;
    cursor: pointer;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    box-shadow: 0 4px 20px rgba(102, 126, 234, 0.3);
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
}

.order-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 30px rgba(102, 126, 234, 0.4);
}

.order-btn:active {
    transform: translateY(0);
}

/* Orders */
.orders-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1.5rem;
}

.refresh-btn {
    background: #28a745;
    color: white;
    border: none;
    padding: 0.5rem 1rem;
    border-radius: 5px;
    cursor: pointer;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.no-orders {
    text-align: center;
    padding: 3rem;
    color: #999;
}

.no-orders i {
    font-size: 3rem;
    margin-bottom: 1rem;
}

.order-card {
    background: white;
    border-radius: 10px;
    padding: 1.5rem;
    margin-bottom: 1rem;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.order-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1rem;
    padding-bottom: 1rem;
    border-bottom: 1px solid #eee;
}

.order-id {
    font-weight: 600;
    color: #333;
}

.order-status {
    padding: 0.25rem 0.75rem;
    border-radius: 15px;
    font-size: 0.8rem;
    font-weight: 600;
}

.order-status.pending {
    background: #fff3cd;
    color: #856404;
}

.order-status.preparing {
    background: #d4edda;
    color: #155724;
}

.order-status.completed {
    background: #d1ecf1;
    color: #0c5460;
}

.order-items {
    margin-bottom: 1rem;
}

.order-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0.5rem 0;
    border-bottom: 1px solid #f8f9fa;
}

.order-item:last-child {
    border-bottom: none;
}

.order-total {
    text-align: right;
    font-size: 1.2rem;
    font-weight: 600;
    color: #ff6b6b;
}

/* Modal */
.modal {
    display: none;
    position: fixed;
    z-index: 1000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.6);
    backdrop-filter: blur(4px);
    animation: fadeIn 0.3s ease;
}

@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

.modal-content {
    background: white;
    margin: 10% auto;
    padding: 0;
    border-radius: 20px;
    width: 90%;
    max-width: 400px;
    position: relative;
    overflow: hidden;
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
    animation: slideUp 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

@keyframes slideUp {
    from {
        transform: translateY(50px);
        opacity: 0;
    }
    to {
        transform: translateY(0);
        opacity: 1;
    }
}

.close {
    position: absolute;
    right: 16px;
    top: 16px;
    font-size: 1.5rem;
    font-weight: bold;
    cursor: pointer;
    z-index: 1001;
    color: white;
    background: rgba(0, 0, 0, 0.5);
    width: 32px;
    height: 32px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s ease;
}

.close:hover {
    background: rgba(0, 0, 0, 0.7);
    transform: scale(1.1);
}

.food-detail {
    display: flex;
    flex-direction: column;
}

.modal-image-container {
    position: relative;
    width: 100%;
    height: 250px;
    overflow: hidden;
    background: linear-gradient(135deg, #f8f9fa, #e9ecef);
    border-radius: 20px 20px 0 0;
}

#modal-food-image {
    width: 100%;
    height: 100%;
    object-fit: cover;
    object-position: center;
    display: block;
    transition: transform 0.3s ease;
}

#modal-food-image:hover {
    transform: scale(1.05);
}

.modal-no-image-placeholder {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    background: linear-gradient(135deg, #667eea, #764ba2);
    color: rgba(255, 255, 255, 0.8);
    font-size: 16px;
    font-weight: 500;
}

.modal-no-image-placeholder i {
    font-size: 48px;
    margin-bottom: 12px;
    opacity: 0.7;
}

.food-detail .food-info {
    padding: 24px;
    background: white;
}

.food-detail .food-info h3 {
    color: #333;
    font-size: 1.4rem;
    font-weight: 700;
    margin-bottom: 8px;
    line-height: 1.3;
}

.food-detail .food-price {
    color: #667eea;
    font-size: 1.3rem;
    font-weight: 700;
    margin-bottom: 20px;
}

.quantity-selector {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 16px;
    margin: 20px 0;
    background: #f8f9fa;
    border-radius: 16px;
    padding: 12px;
}

.quantity {
    font-size: 1.3rem;
    font-weight: 700;
    min-width: 40px;
    text-align: center;
    color: #333;
}

.add-to-cart-btn {
    width: 100%;
    background: linear-gradient(135deg, #667eea, #764ba2);
    color: white;
    border: none;
    padding: 16px;
    border-radius: 16px;
    font-size: 1.1rem;
    font-weight: 700;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    box-shadow: 0 4px 20px rgba(102, 126, 234, 0.3);
}

.add-to-cart-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 30px rgba(102, 126, 234, 0.4);
}

.add-to-cart-btn:active {
    transform: translateY(0);
}

/* Order Type Selection */
.order-type-selection {
    background: white;
    border-radius: 15px;
    padding: 2rem;
    margin-bottom: 2rem;
    box-shadow: 0 4px 20px rgba(0,0,0,0.1);
    text-align: center;
}

.order-type-selection h3 {
    color: #333;
    margin-bottom: 2rem;
    font-size: 1.5rem;
    font-weight: 600;
}

.order-type-options {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 2rem;
    max-width: 800px;
    margin: 0 auto;
}

.order-type-card {
    background: linear-gradient(135deg, #f8f9fa, #ffffff);
    border: 2px solid #e9ecef;
    border-radius: 15px;
    padding: 2rem;
    text-align: center;
    transition: all 0.3s ease;
    cursor: pointer;
    position: relative;
    overflow: hidden;
}

.order-type-card:hover {
    transform: translateY(-5px);
    border-color: #ff6b6b;
    box-shadow: 0 8px 25px rgba(255, 107, 107, 0.15);
}

.order-type-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 107, 107, 0.1), transparent);
    transition: left 0.5s ease;
}

.order-type-card:hover::before {
    left: 100%;
}

.order-type-icon {
    width: 80px;
    height: 80px;
    background: linear-gradient(135deg, #ff6b6b, #ee5a24);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 1.5rem;
    color: white;
    font-size: 2rem;
    box-shadow: 0 4px 15px rgba(255, 107, 107, 0.3);
}

.order-type-card h4 {
    color: #333;
    margin-bottom: 1rem;
    font-size: 1.3rem;
    font-weight: 600;
}

.order-type-card p {
    color: #666;
    margin-bottom: 1.5rem;
    line-height: 1.6;
}

/* .buffet-price removed - price will be shown when selecting buffet package */

.select-type-btn {
    background: linear-gradient(135deg, #ff6b6b, #ee5a24);
    color: white;
    border: none;
    padding: 1rem 2rem;
    border-radius: 25px;
    font-size: 1rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: 0 4px 15px rgba(255, 107, 107, 0.3);
    position: relative;
    overflow: hidden;
}

.select-type-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(255, 107, 107, 0.4);
}

.select-type-btn:active {
    transform: translateY(0);
}

/* Order Section */
.order-section {
    background: white;
    border-radius: 15px;
    padding: 2rem;
    box-shadow: 0 4px 20px rgba(0,0,0,0.1);
}

.section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 2rem;
    padding-bottom: 1rem;
    border-bottom: 2px solid #f8f9fa;
}

.section-header h3 {
    color: #333;
    font-size: 1.4rem;
    font-weight: 600;
}

.back-to-selection-btn {
    background: #6c757d;
    color: white;
    border: none;
    padding: 0.8rem 1.5rem;
    border-radius: 25px;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-weight: 500;
}

.back-to-selection-btn:hover {
    background: #5a6268;
    transform: translateY(-2px);
}

/* Buffet Packages Grid */
.buffet-packages-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));
    gap: 1.5rem;
    margin-bottom: 2rem;
}

.buffet-package-card {
    background: white;
    border-radius: 20px;
    padding: 1.5rem;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
    border: 2px solid transparent;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    cursor: pointer;
    position: relative;
    overflow: hidden;
}

.buffet-package-card:hover {
    transform: translateY(-8px);
    box-shadow: 0 16px 48px rgba(0, 0, 0, 0.15);
    border-color: #667eea;
}

.buffet-package-card.popular {
    border-color: #ff6b6b;
    background: linear-gradient(135deg, #fff5f5, #ffffff);
}

.buffet-package-card.popular::before {
    content: 'PHỔ BIẾN';
    position: absolute;
    top: 15px;
    right: -25px;
    background: linear-gradient(135deg, #ff6b6b, #ee5a24);
    color: white;
    padding: 4px 30px;
    font-size: 0.7rem;
    font-weight: 700;
    transform: rotate(45deg);
    letter-spacing: 0.5px;
}

.buffet-package-header {
    text-align: center;
    margin-bottom: 1.5rem;
}

.buffet-package-icon {
    width: 70px;
    height: 70px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 1rem;
    font-size: 1.8rem;
    color: white;
    box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);
}

.buffet-package-icon.primary {
    background: linear-gradient(135deg, #667eea, #764ba2);
}

.buffet-package-icon.warning {
    background: linear-gradient(135deg, #f59e0b, #d97706);
}

.buffet-package-icon.success {
    background: linear-gradient(135deg, #10b981, #059669);
}

.buffet-package-icon.info {
    background: linear-gradient(135deg, #06b6d4, #0891b2);
}

.buffet-package-icon.danger {
    background: linear-gradient(135deg, #ef4444, #dc2626);
}

.buffet-package-icon.dark {
    background: linear-gradient(135deg, #374151, #1f2937);
}

.buffet-package-icon.purple {
    background: linear-gradient(135deg, #8b5cf6, #7c3aed);
}

.buffet-package-name {
    font-size: 1.3rem;
    font-weight: 700;
    color: #333;
    margin-bottom: 0.5rem;
}

.buffet-package-description {
    color: #666;
    font-size: 0.9rem;
    margin-bottom: 1rem;
}

.buffet-package-price {
    font-size: 1.8rem;
    font-weight: 800;
    color: #667eea;
    margin-bottom: 1.5rem;
}

.buffet-package-features {
    text-align: left;
    margin-bottom: 1.5rem;
}

.buffet-package-features ul {
    list-style: none;
    padding: 0;
    margin: 0;
}

.buffet-package-features li {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    margin-bottom: 0.5rem;
    font-size: 0.85rem;
    color: #555;
}

.buffet-package-features i {
    color: #10b981;
    font-size: 0.8rem;
}

.select-buffet-btn {
    width: 100%;
    background: linear-gradient(135deg, #667eea, #764ba2);
    color: white;
    border: none;
    padding: 0.8rem 1.5rem;
    border-radius: 12px;
    font-weight: 600;
    font-size: 0.9rem;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
}

.select-buffet-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 24px rgba(102, 126, 234, 0.3);
}

/* Buffet Section */
.buffet-info {
    text-align: center;
    max-width: 600px;
    margin: 0 auto;
}

.buffet-actions {
    display: flex;
    gap: 1rem;
    justify-content: center;
    align-items: center;
    flex-wrap: wrap;
}

.back-to-packages-btn {
    background: #6c757d;
    color: white;
    border: none;
    padding: 0.8rem 1.5rem;
    border-radius: 12px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.back-to-packages-btn:hover {
    background: #5a6268;
    transform: translateY(-2px);
}

/* Responsive Design for Buffet Packages */
@media (max-width: 768px) {
    .buffet-packages-grid {
        grid-template-columns: 1fr;
        gap: 1rem;
        margin-bottom: 1.5rem;
    }

    .buffet-package-card {
        padding: 1.25rem;
    }

    .buffet-package-icon {
        width: 60px;
        height: 60px;
        font-size: 1.5rem;
    }

    .buffet-package-name {
        font-size: 1.2rem;
    }

    .buffet-package-price {
        font-size: 1.6rem;
    }

    .buffet-package-features li {
        font-size: 0.8rem;
    }

    .buffet-actions {
        flex-direction: column;
        gap: 0.75rem;
    }

    .back-to-packages-btn,
    .order-buffet-btn {
        width: 100%;
        justify-content: center;
    }
}

@media (max-width: 480px) {
    .buffet-packages-grid {
        gap: 0.75rem;
    }

    .buffet-package-card {
        padding: 1rem;
    }

    .buffet-package-icon {
        width: 50px;
        height: 50px;
        font-size: 1.3rem;
    }

    .buffet-package-name {
        font-size: 1.1rem;
    }

    .buffet-package-price {
        font-size: 1.4rem;
    }

    .buffet-package-features {
        margin-bottom: 1rem;
    }

    .select-buffet-btn {
        padding: 0.7rem 1.25rem;
        font-size: 0.85rem;
    }
}

.buffet-details {
    background: #f8f9fa;
    border-radius: 15px;
    padding: 2rem;
    margin-bottom: 2rem;
}

.buffet-details h4 {
    color: #333;
    margin-bottom: 1.5rem;
    font-size: 1.3rem;
}

.buffet-details p {
    color: #666;
    margin-bottom: 1rem;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
}

.buffet-details i {
    color: #28a745;
}

.buffet-price-display {
    margin-top: 1.5rem;
    font-size: 1.2rem;
}

.buffet-price-display .price {
    color: #ff6b6b;
    font-weight: 700;
    font-size: 1.5rem;
}

.order-buffet-btn {
    background: linear-gradient(135deg, #28a745, #20c997);
    color: white;
    border: none;
    padding: 1.2rem 3rem;
    border-radius: 25px;
    font-size: 1.2rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: 0 4px 15px rgba(40, 167, 69, 0.3);
    display: flex;
    align-items: center;
    gap: 0.8rem;
    margin: 0 auto;
}

.order-buffet-btn:hover:not(:disabled) {
    transform: translateY(-3px);
    box-shadow: 0 6px 20px rgba(40, 167, 69, 0.4);
}

.order-buffet-btn:disabled {
    background: #ccc;
    cursor: not-allowed;
    opacity: 0.6;
    box-shadow: none;
}

/* Free Drink Selection */
.free-drink-section {
    margin-top: 1.5rem;
    text-align: left;
}

.free-drink-section h5 {
    color: #333;
    margin-bottom: 1rem;
    font-size: 1.1rem;
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.free-drink-section h5 i {
    color: #ff6b6b;
}

.drinks-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(120px, 1fr));
    gap: 12px;
    margin-bottom: 1rem;
}

.drink-item {
    background: white;
    border: 2px solid #e9ecef;
    border-radius: 12px;
    padding: 12px;
    text-align: center;
    cursor: pointer;
    transition: all 0.3s ease;
    position: relative;
}

.drink-item:hover {
    border-color: #28a745;
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(40, 167, 69, 0.2);
}

.drink-item.selected {
    border-color: #28a745;
    background: linear-gradient(135deg, #28a745, #20c997);
    color: white;
}

.drink-item .drink-image {
    width: 40px;
    height: 40px;
    border-radius: 8px;
    object-fit: cover;
    margin: 0 auto 8px;
    display: block;
}

.drink-item .drink-name {
    font-size: 0.9rem;
    font-weight: 600;
    margin-bottom: 4px;
}

.drink-item .drink-price {
    font-size: 0.8rem;
    opacity: 0.7;
}

.drink-item.selected .drink-price {
    text-decoration: line-through;
}

.drink-item.selected::after {
    content: "MIỄN PHÍ";
    position: absolute;
    top: -8px;
    right: -8px;
    background: #ff6b6b;
    color: white;
    font-size: 0.7rem;
    font-weight: 700;
    padding: 2px 6px;
    border-radius: 8px;
}

/* Buffet Active Section */
.buffet-active {
    padding: 20px 0;
}

.buffet-status {
    background: linear-gradient(135deg, #28a745, #20c997);
    color: white;
    padding: 20px;
    border-radius: 16px;
    margin-bottom: 24px;
    text-align: center;
}

.buffet-status-header {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 12px;
    margin-bottom: 8px;
}

.buffet-status-header i {
    font-size: 1.5rem;
}

.buffet-status h4 {
    margin: 0;
    font-size: 1.3rem;
    font-weight: 700;
}

.buffet-status p {
    margin: 0;
    opacity: 0.9;
    font-size: 1rem;
}

/* Notification */
.notification {
    position: fixed;
    top: 20px;
    right: 20px;
    background: #28a745;
    color: white;
    padding: 1rem 1.5rem;
    border-radius: 8px;
    box-shadow: 0 4px 12px rgba(0,0,0,0.15);
    transform: translateX(400px);
    transition: transform 0.3s ease;
    z-index: 1000;
}

.notification.show {
    transform: translateX(0);
}

.notification-content {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

/* Responsive Design */

/* Tablet Landscape (1024px and down) */
@media (max-width: 1024px) {
    .foods-grid {
        grid-template-columns: repeat(auto-fill, minmax(180px, 1fr));
        gap: 20px;
        padding: 24px;
    }

    .container {
        padding: 0 20px;
    }
}

/* Tablet Portrait (768px and down) */
@media (max-width: 768px) {
    .header h1 {
        font-size: 1.2rem;
    }

    .table-info {
        font-size: 0.8rem;
        padding: 6px 12px;
    }

    .foods-grid {
        grid-template-columns: repeat(auto-fill, minmax(160px, 1fr));
        gap: 16px;
        padding: 20px;
    }

    .food-image-container {
        height: 100px;
    }

    .food-name {
        font-size: 0.85rem;
    }

    .food-price {
        font-size: 0.9rem;
    }

    .modal-content {
        width: 95%;
        margin: 5% auto;
        max-width: none;
    }

    .modal-image-container {
        height: 200px;
        border-radius: 16px 16px 0 0;
    }
}

/* Mobile (480px and down) */
@media (max-width: 480px) {
    .container {
        padding: 0 12px;
    }

    .header {
        padding: 12px 0;
    }

    .header h1 {
        font-size: 1.1rem;
    }

    .table-info {
        font-size: 0.75rem;
        padding: 4px 8px;
    }

    .nav-tabs {
        top: 62px;
    }

    .tab-btn {
        padding: 12px 8px;
        font-size: 0.75rem;
        min-height: 50px;
    }

    .tab-btn i {
        font-size: 1rem;
    }

    .tab-content {
        min-height: calc(100vh - 112px);
        padding: 16px 0 80px;
    }

    .search-box {
        margin-bottom: 16px;
    }

    .search-box input {
        padding: 12px 12px 12px 40px;
        font-size: 0.9rem;
    }

    .search-box i {
        left: 12px;
        font-size: 1rem;
    }

    .category {
        margin-bottom: 20px;
        border-radius: 16px;
    }

    .category-header {
        padding: 12px 16px;
        font-size: 1rem;
    }

    .foods-grid {
        grid-template-columns: repeat(2, 1fr);
        gap: 12px;
        padding: 16px;
    }

    .food-item {
        border-radius: 12px;
    }

    .food-image-container {
        height: 90px;
    }

    .food-info {
        padding: 10px;
    }

    .food-name {
        font-size: 0.8rem;
        margin-bottom: 4px;
    }

    .food-price {
        font-size: 0.85rem;
    }

    .cart-item {
        padding: 12px;
        margin-bottom: 8px;
        border-radius: 12px;
    }

    .cart-item-image-container {
        width: 40px;
        height: 40px;
        border-radius: 8px;
    }

    .cart-item-name {
        font-size: 0.85rem;
    }

    .cart-item-price {
        font-size: 0.8rem;
    }

    .qty-btn {
        width: 24px;
        height: 24px;
        font-size: 0.7rem;
    }

    .cart-item-quantity {
        font-size: 0.85rem;
        min-width: 20px;
    }

    .remove-item {
        padding: 4px;
        font-size: 0.7rem;
    }

    .cart-summary {
        padding: 16px;
        bottom: 16px;
        border-radius: 16px;
    }

    .total-row {
        font-size: 1rem;
        margin-bottom: 12px;
    }

    .total-amount {
        font-size: 1.2rem;
    }

    .order-btn {
        padding: 14px;
        font-size: 1rem;
        border-radius: 12px;
    }

    .modal-content {
        width: 98%;
        margin: 2% auto;
        border-radius: 16px;
    }

    .modal-image-container {
        height: 180px;
        border-radius: 16px 16px 0 0;
    }

    .food-detail .food-info {
        padding: 20px;
    }

    .food-detail .food-name {
        font-size: 1.1rem;
    }

    .food-detail .food-price {
        font-size: 1.2rem;
    }

    .quantity-selector {
        gap: 12px;
        margin: 16px 0;
        padding: 10px;
        border-radius: 12px;
    }

    .quantity {
        font-size: 1.1rem;
        min-width: 32px;
    }

    .add-to-cart-btn {
        padding: 14px;
        font-size: 1rem;
        border-radius: 12px;
    }

    .close {
        right: 12px;
        top: 12px;
        width: 28px;
        height: 28px;
        font-size: 1.2rem;
    }
}

/* Extra Small Mobile (360px and down) */
@media (max-width: 360px) {
    .foods-grid {
        grid-template-columns: 1fr 1fr;
        gap: 8px;
        padding: 12px;
    }

    .food-image-container {
        height: 80px;
    }

    .food-info {
        padding: 8px;
    }

    .food-name {
        font-size: 0.75rem;
    }

    .food-price {
        font-size: 0.8rem;
    }

    .cart-summary {
        padding: 12px;
    }

    .modal-content {
        width: 100%;
        margin: 0;
        border-radius: 0;
        height: 100vh;
    }

    .modal-image-container {
        height: 140px;
    }

    .payment-modal-content {
        width: 95%;
        margin: 20px auto;
    }

    .payment-actions {
        flex-direction: column;
    }

    .cancel-payment-btn,
    .process-payment-btn {
        width: 100%;
        justify-content: center;
    }
}

/* Payment Modal Styles */
.payment-modal-content {
    max-width: 500px;
    width: 90%;
    max-height: 90vh;
    overflow-y: auto;
}

.payment-header {
    text-align: center;
    margin-bottom: 24px;
    padding-bottom: 16px;
    border-bottom: 1px solid rgba(0, 0, 0, 0.1);
}

.payment-header h3 {
    color: #333;
    margin-bottom: 12px;
    font-size: 1.3rem;
}

.payment-order-info p {
    margin: 4px 0;
    color: #666;
    font-size: 0.95rem;
}

.payment-total {
    font-weight: 700;
    color: #667eea;
    font-size: 1.1rem;
}

.payment-methods {
    margin-bottom: 24px;
}

.payment-methods h4 {
    margin-bottom: 16px;
    color: #333;
    font-size: 1rem;
}

.payment-method-option {
    margin-bottom: 12px;
}

.payment-method-option input[type="radio"] {
    display: none;
}

.payment-method-label {
    display: flex;
    align-items: center;
    gap: 12px;
    padding: 16px;
    border: 2px solid #e9ecef;
    border-radius: 12px;
    cursor: pointer;
    transition: all 0.3s ease;
    background: white;
}

.payment-method-label:hover {
    border-color: #667eea;
    background: rgba(102, 126, 234, 0.05);
}

.payment-method-option input[type="radio"]:checked + .payment-method-label {
    border-color: #667eea;
    background: linear-gradient(135deg, rgba(102, 126, 234, 0.1), rgba(118, 75, 162, 0.1));
    color: #667eea;
}

.payment-method-label i {
    font-size: 1.2rem;
    width: 24px;
    text-align: center;
}

.payment-method-label span {
    font-weight: 600;
    font-size: 1rem;
}

.payment-section {
    background: rgba(102, 126, 234, 0.05);
    border-radius: 12px;
    padding: 20px;
    margin-bottom: 20px;
    border: 1px solid rgba(102, 126, 234, 0.2);
}

.payment-section h4 {
    margin-bottom: 16px;
    color: #667eea;
    font-size: 1rem;
    display: flex;
    align-items: center;
    gap: 8px;
}

.cash-input-group {
    margin-bottom: 16px;
}

.cash-input-group label {
    display: block;
    margin-bottom: 8px;
    font-weight: 600;
    color: #333;
    font-size: 0.95rem;
}

.cash-input-group input {
    width: 100%;
    padding: 12px 16px;
    border: 2px solid #e9ecef;
    border-radius: 8px;
    font-size: 1rem;
    transition: all 0.3s ease;
    background: white;
}

.cash-input-group input:focus {
    outline: none;
    border-color: #667eea;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.cash-input-group small {
    display: block;
    margin-top: 4px;
    color: #666;
    font-size: 0.85rem;
}

.momo-info {
    text-align: center;
}

.momo-info p {
    margin: 8px 0;
    color: #666;
    font-size: 0.95rem;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
}

.momo-info i {
    color: #667eea;
}

.payment-actions {
    display: flex;
    gap: 12px;
    justify-content: flex-end;
    margin-top: 24px;
    padding-top: 16px;
    border-top: 1px solid rgba(0, 0, 0, 0.1);
}

.cancel-payment-btn,
.process-payment-btn {
    padding: 12px 24px;
    border: none;
    border-radius: 8px;
    font-size: 1rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 8px;
}

.cancel-payment-btn {
    background: #6c757d;
    color: white;
}

.cancel-payment-btn:hover {
    background: #5a6268;
    transform: translateY(-2px);
}

.process-payment-btn {
    background: linear-gradient(135deg, #667eea, #764ba2);
    color: white;
    box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
}

.process-payment-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(102, 126, 234, 0.4);
}

.process-payment-btn:active {
    transform: translateY(0);
}

/* Payment Status Styles */
.payment-status-row {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin: 12px 0;
    padding: 8px 0;
    border-bottom: 1px solid rgba(0, 0, 0, 0.1);
    font-size: 0.9rem;
}

.payment-status {
    font-weight: 600;
    padding: 4px 8px;
    border-radius: 6px;
    font-size: 0.85rem;
}

.payment-status.paid {
    background: linear-gradient(135deg, #10ac84, #00d2d3);
    color: white;
}

.payment-status.pending {
    background: linear-gradient(135deg, #ff9f43, #feca57);
    color: white;
}

.payment-status.unpaid {
    background: linear-gradient(135deg, #ff6b6b, #ee5a24);
    color: white;
}

/* Payment Button in Order Card */
.payment-section {
    margin-top: 16px;
    padding-top: 16px;
    border-top: 1px solid rgba(0, 0, 0, 0.1);
    text-align: center;
}

.payment-btn {
    background: linear-gradient(135deg, #667eea, #764ba2);
    color: white;
    border: none;
    padding: 12px 24px;
    border-radius: 8px;
    font-size: 1rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    display: inline-flex;
    align-items: center;
    gap: 8px;
    box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
}

.payment-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(102, 126, 234, 0.4);
}

.payment-btn:active {
    transform: translateY(0);
}

.payment-btn i {
    font-size: 1.1rem;
}

/* Responsive Design */
@media (max-width: 768px) {
    .foods-grid {
        grid-template-columns: repeat(auto-fill, minmax(140px, 1fr));
        gap: 12px;
        padding: 16px;
    }

    .food-item {
        border-radius: 12px;
    }

    .food-image-container {
        height: 100px;
    }

    .food-info {
        padding: 10px;
    }

    .food-name {
        font-size: 0.85rem;
    }

    .food-price {
        font-size: 0.9rem;
    }

    .cart-item {
        padding: 12px;
        gap: 10px;
    }

    .cart-item-image-container {
        width: 45px;
        height: 45px;
    }

    .cart-item-name {
        font-size: 0.85rem;
    }

    .cart-item-price {
        font-size: 0.8rem;
    }

    .qty-btn {
        width: 26px;
        height: 26px;
        font-size: 0.75rem;
    }

    .cart-item-quantity {
        font-size: 0.85rem;
    }

    .cart-summary {
        padding: 16px;
        margin: 0 -8px;
    }

    .place-order-btn {
        padding: 14px;
        font-size: 0.9rem;
    }

    .modal-content {
        margin: 10% auto;
        padding: 16px;
        width: 90%;
        max-width: 400px;
    }

    .payment-modal-content {
        max-width: 400px;
    }

    .payment-actions {
        flex-direction: column;
    }

    .cancel-payment-btn,
    .process-payment-btn {
        width: 100%;
        justify-content: center;
    }

    .food-detail {
        flex-direction: column;
        text-align: center;
    }

    .modal-image-container {
        width: 100%;
        height: 200px;
        margin-bottom: 16px;
    }

    .food-info h3 {
        font-size: 1.2rem;
    }

    .quantity-selector {
        justify-content: center;
    }

    .add-to-cart-btn {
        width: 100%;
        margin-top: 16px;
    }

    .header h1 {
        font-size: 1.2rem;
    }

    .table-info {
        font-size: 0.8rem;
        padding: 6px 12px;
    }

    .tab-btn {
        font-size: 0.8rem;
        padding: 12px 8px;
        min-height: 55px;
    }

    .tab-btn i {
        font-size: 1.1rem;
    }

    .search-box input {
        padding: 14px 14px 14px 44px;
        font-size: 0.9rem;
    }

    .category-header {
        padding: 14px 16px;
        font-size: 1rem;
    }

    .notification {
        bottom: 80px;
        left: 16px;
        right: 16px;
        width: auto;
    }
}

/* Payment Modal Styles */
.payment-modal-content {
    max-width: 500px;
    width: 90%;
}

.payment-header {
    text-align: center;
    margin-bottom: 30px;
    padding-bottom: 20px;
    border-bottom: 1px solid rgba(0, 0, 0, 0.1);
}

.payment-header h3 {
    color: #333;
    margin-bottom: 15px;
    font-size: 1.4rem;
    font-weight: 600;
}

.payment-order-info {
    background: linear-gradient(135deg, rgba(102, 126, 234, 0.1), rgba(118, 75, 162, 0.1));
    padding: 15px;
    border-radius: 12px;
    margin-top: 15px;
}

.payment-order-info p {
    margin: 5px 0;
    font-size: 0.95rem;
    color: #666;
}

.payment-total {
    font-weight: 600;
    font-size: 1.1rem;
    color: #333;
}

.payment-total span {
    color: #667eea;
    font-weight: 700;
}

.payment-methods {
    margin-bottom: 30px;
}

.payment-methods h4 {
    margin-bottom: 20px;
    color: #333;
    font-size: 1.1rem;
}

.payment-method-option {
    margin-bottom: 15px;
}

.payment-method-option input[type="radio"] {
    display: none;
}

.payment-method-label {
    display: flex;
    align-items: center;
    padding: 15px 20px;
    border: 2px solid #e0e0e0;
    border-radius: 12px;
    cursor: pointer;
    transition: all 0.3s ease;
    background: white;
    font-weight: 500;
}

.payment-method-label:hover {
    border-color: #667eea;
    background: rgba(102, 126, 234, 0.05);
}

.payment-method-option input[type="radio"]:checked + .payment-method-label {
    border-color: #667eea;
    background: linear-gradient(135deg, rgba(102, 126, 234, 0.1), rgba(118, 75, 162, 0.1));
    color: #667eea;
}

.payment-method-label i {
    font-size: 1.3rem;
    margin-right: 12px;
    width: 24px;
    text-align: center;
}

.payment-section {
    background: #f8f9fa;
    padding: 20px;
    border-radius: 12px;
    margin-bottom: 20px;
}

.payment-section h4 {
    margin-bottom: 15px;
    color: #333;
    font-size: 1rem;
    display: flex;
    align-items: center;
    gap: 8px;
}

.cash-input-group {
    margin-bottom: 15px;
}

.cash-input-group label {
    display: block;
    margin-bottom: 8px;
    font-weight: 500;
    color: #333;
}

.cash-input-group input {
    width: 100%;
    padding: 12px 15px;
    border: 2px solid #e0e0e0;
    border-radius: 8px;
    font-size: 1rem;
    transition: border-color 0.3s ease;
}

.cash-input-group input:focus {
    outline: none;
    border-color: #667eea;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.cash-input-group small {
    color: #666;
    font-size: 0.85rem;
    margin-top: 5px;
    display: block;
}

.momo-info {
    text-align: center;
}

.momo-info p {
    margin: 10px 0;
    color: #666;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
}

.momo-info i {
    color: #667eea;
}

/* MoMo QR Code Styles */
.momo-qr-container {
    margin-top: 20px;
    padding: 20px;
    background: white;
    border-radius: 12px;
    border: 2px solid #667eea;
}

.qr-code-section {
    text-align: center;
}

.qr-code-section h5 {
    margin-bottom: 20px;
    color: #333;
    font-size: 1.1rem;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
}

.qr-code-wrapper {
    position: relative;
    display: inline-block;
    margin-bottom: 20px;
}

.qr-code-image {
    width: 200px;
    height: 200px;
    border: 2px solid #e0e0e0;
    border-radius: 8px;
    background: white;
}

.qr-loading {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    background: rgba(255, 255, 255, 0.9);
    border-radius: 8px;
}

.qr-loading i {
    font-size: 2rem;
    color: #667eea;
    margin-bottom: 10px;
}

.qr-loading p {
    color: #666;
    margin: 0;
}

.qr-instructions {
    margin-bottom: 20px;
}

.qr-instructions p {
    margin: 8px 0;
    color: #666;
    font-size: 0.9rem;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
}

.qr-instructions i {
    color: #667eea;
    width: 16px;
}

.qr-actions {
    display: flex;
    gap: 10px;
    justify-content: center;
    margin-bottom: 20px;
}

.open-momo-btn,
.refresh-qr-btn {
    padding: 10px 16px;
    border: none;
    border-radius: 8px;
    font-size: 0.9rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 6px;
}

.open-momo-btn {
    background: linear-gradient(135deg, #667eea, #764ba2);
    color: white;
}

.open-momo-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
}

.refresh-qr-btn {
    background: #f8f9fa;
    color: #666;
    border: 1px solid #e0e0e0;
}

.refresh-qr-btn:hover {
    background: #e9ecef;
    transform: translateY(-2px);
}

.payment-status {
    padding: 15px;
    background: #f8f9fa;
    border-radius: 8px;
    border-left: 4px solid #667eea;
}

.status-checking {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 10px;
    color: #667eea;
    font-weight: 500;
}

.status-checking i {
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0% { opacity: 1; }
    50% { opacity: 0.5; }
    100% { opacity: 1; }
}

.status-success {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 10px;
    color: #28a745;
    font-weight: 500;
}

.status-failed {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 10px;
    color: #dc3545;
    font-weight: 500;
}

.payment-actions {
    display: flex;
    gap: 15px;
    justify-content: center;
    margin-top: 30px;
}

.cancel-payment-btn,
.process-payment-btn {
    padding: 12px 24px;
    border: none;
    border-radius: 8px;
    font-size: 1rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 8px;
    min-width: 120px;
    justify-content: center;
}

.cancel-payment-btn {
    background: #6c757d;
    color: white;
}

.cancel-payment-btn:hover {
    background: #5a6268;
    transform: translateY(-2px);
}

.process-payment-btn {
    background: linear-gradient(135deg, #667eea, #764ba2);
    color: white;
    box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
}

.process-payment-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(102, 126, 234, 0.4);
}

.process-payment-btn:disabled {
    background: #ccc;
    cursor: not-allowed;
    transform: none;
    box-shadow: none;
}

/* Payment Modal Responsive */
@media (max-width: 768px) {
    .payment-modal-content {
        width: 95%;
        margin: 10% auto;
    }

    .payment-actions {
        flex-direction: column;
    }

    .cancel-payment-btn,
    .process-payment-btn {
        width: 100%;
    }

    .payment-method-label {
        padding: 12px 15px;
    }

    .payment-section {
        padding: 15px;
    }
}
