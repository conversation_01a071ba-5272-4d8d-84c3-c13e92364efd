const axios = require('axios');

// Cấu hình API
const API_BASE_URL = 'http://localhost:3000'; // API Gateway port
const ADMIN_USERNAME = 'admin';
const ADMIN_PASSWORD = 'admin123';

// Hàm đăng nhập để lấy token admin
async function loginAdmin() {
  try {
    const response = await axios.post(`${API_BASE_URL}/api/users/login`, {
      username: ADMIN_USERNAME,
      password: ADMIN_PASSWORD
    });
    
    return response.data.token;
  } catch (error) {
    console.error('Lỗi đăng nhập admin:', error.response?.data?.message || error.message);
    throw error;
  }
}

// Hàm tạo đơn hàng test
async function createTestOrder() {
  try {
    console.log('🔄 Tạo đơn hàng test...');
    
    const token = await loginAdmin();
    
    // Tạo đơn hàng mới
    const orderData = {
      table_id: 1,
      items: [
        { food_id: 1, quantity: 2 },
        { food_id: 2, quantity: 1 }
      ]
    };

    const response = await axios.post(`${API_BASE_URL}/api/orders`, orderData, {
      headers: { 'Authorization': `Bearer ${token}` }
    });

    const order = response.data;
    console.log('✅ Đã tạo đơn hàng test:', order.id);
    console.log('💰 Tổng tiền:', order.total);
    
    return order;
  } catch (error) {
    console.error('❌ Lỗi tạo đơn hàng:', error.response?.data?.message || error.message);
    throw error;
  }
}

// Hàm test thanh toán MoMo
async function testMoMoPayment(orderId, amount) {
  try {
    console.log('\n🔄 Test thanh toán MoMo...');
    
    const paymentData = {
      orderId: orderId,
      amount: amount,
      orderInfo: `Test thanh toán đơn hàng #${orderId}`,
      tableId: 1
    };

    const response = await axios.post(`${API_BASE_URL}/api/payments/momo/create`, paymentData);
    
    if (response.data.success) {
      console.log('✅ Tạo thanh toán MoMo thành công!');
      console.log('🔗 URL thanh toán:', response.data.payUrl);
      console.log('📱 Mở URL này để test thanh toán MoMo sandbox');
      
      return response.data;
    } else {
      console.log('❌ Lỗi tạo thanh toán:', response.data.message);
    }
  } catch (error) {
    console.error('❌ Lỗi test MoMo:', error.response?.data?.message || error.message);
    throw error;
  }
}

// Hàm test thanh toán tiền mặt
async function testCashPayment(orderId, amount) {
  try {
    console.log('\n🔄 Test thanh toán tiền mặt...');
    
    const token = await loginAdmin();
    
    const paymentData = {
      orderId: orderId,
      amount: amount,
      cashReceived: amount + 50000 // Thêm 50k tiền thừa
    };

    const response = await axios.post(`${API_BASE_URL}/api/payments/cash`, paymentData, {
      headers: { 'Authorization': `Bearer ${token}` }
    });
    
    if (response.data.success) {
      console.log('✅ Thanh toán tiền mặt thành công!');
      console.log('💰 Tiền thừa:', response.data.cashChange);
      
      return response.data;
    } else {
      console.log('❌ Lỗi thanh toán:', response.data.message);
    }
  } catch (error) {
    console.error('❌ Lỗi test tiền mặt:', error.response?.data?.message || error.message);
    throw error;
  }
}

// Hàm kiểm tra trạng thái thanh toán
async function checkPaymentStatus(orderId) {
  try {
    console.log('\n🔍 Kiểm tra trạng thái thanh toán...');
    
    const response = await axios.get(`${API_BASE_URL}/api/payments/status/${orderId}`);
    
    console.log('📊 Trạng thái thanh toán:', response.data);
    
    return response.data;
  } catch (error) {
    console.error('❌ Lỗi kiểm tra trạng thái:', error.response?.data?.message || error.message);
    throw error;
  }
}

// Hàm test đầy đủ
async function runPaymentTests() {
  try {
    console.log('🚀 Bắt đầu test chức năng thanh toán...\n');
    
    // 1. Tạo đơn hàng test
    const order = await createTestOrder();
    
    // 2. Test thanh toán MoMo
    await testMoMoPayment(order.id, order.total);
    
    // 3. Kiểm tra trạng thái
    await checkPaymentStatus(order.id);
    
    console.log('\n✅ Hoàn thành test thanh toán!');
    console.log('\n📝 Hướng dẫn test:');
    console.log('1. Mở URL thanh toán MoMo ở trên');
    console.log('2. Sử dụng thông tin test MoMo sandbox:');
    console.log('   - Số điện thoại: 0963181714');
    console.log('   - OTP: 123456');
    console.log('3. Hoàn thành thanh toán và kiểm tra kết quả');
    
  } catch (error) {
    console.error('💥 Lỗi trong quá trình test:', error.message);
  }
}

// Chạy test nếu file được gọi trực tiếp
if (require.main === module) {
  runPaymentTests();
}

module.exports = {
  createTestOrder,
  testMoMoPayment,
  testCashPayment,
  checkPaymentStatus,
  runPaymentTests
};
