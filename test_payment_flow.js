const axios = require('axios');

// <PERSON><PERSON><PERSON> hình
const API_BASE_URL = 'http://localhost:3000';
const TABLE_SERVICE_URL = 'http://localhost:3011';
const ADMIN_USERNAME = 'admin';
const ADMIN_PASSWORD = 'admin123';

// <PERSON><PERSON>u sắc cho console
const colors = {
    reset: '\x1b[0m',
    bright: '\x1b[1m',
    red: '\x1b[31m',
    green: '\x1b[32m',
    yellow: '\x1b[33m',
    blue: '\x1b[34m',
    magenta: '\x1b[35m',
    cyan: '\x1b[36m'
};

function log(message, color = 'reset') {
    console.log(`${colors[color]}${message}${colors.reset}`);
}

// Đăng nhập admin
async function loginAdmin() {
    try {
        const response = await axios.post(`${API_BASE_URL}/api/users/login`, {
            username: ADMIN_USERNAME,
            password: ADMIN_PASSWORD
        });
        return response.data.token;
    } catch (error) {
        throw new Error(`Lỗi đăng nhập: ${error.response?.data?.message || error.message}`);
    }
}

// Tạo QR key cho bàn
async function createTableKey(tableId, token) {
    try {
        const response = await axios.post(`${API_BASE_URL}/api/tables/${tableId}/qr`, {}, {
            headers: { 'Authorization': `Bearer ${token}` }
        });
        return response.data;
    } catch (error) {
        throw new Error(`Lỗi tạo QR key: ${error.response?.data?.message || error.message}`);
    }
}

// Tạo đơn hàng từ khách hàng
async function createCustomerOrder(tableId, tableKey) {
    try {
        const orderData = {
            table_id: tableId,
            table_key: tableKey,
            items: [
                { food_id: 1, quantity: 2 },
                { food_id: 2, quantity: 1 }
            ]
        };

        const response = await axios.post(`${TABLE_SERVICE_URL}/api/customer/orders`, orderData);
        return response.data;
    } catch (error) {
        throw new Error(`Lỗi tạo đơn hàng: ${error.response?.data?.message || error.message}`);
    }
}

// Cập nhật trạng thái đơn hàng thành "Hoàn thành"
async function completeOrder(orderId, token) {
    try {
        await axios.put(`${API_BASE_URL}/api/orders/${orderId}/status`, {
            status: 'Hoàn thành'
        }, {
            headers: { 'Authorization': `Bearer ${token}` }
        });
        log(`✅ Đã cập nhật đơn hàng #${orderId} thành "Hoàn thành"`, 'green');
    } catch (error) {
        log(`⚠️ Không thể cập nhật trạng thái đơn hàng: ${error.response?.data?.message || error.message}`, 'yellow');
    }
}

// Test thanh toán MoMo
async function testMoMoPayment(orderId, amount) {
    try {
        const paymentData = {
            orderId: orderId,
            amount: amount,
            orderInfo: `Test thanh toán đơn hàng #${orderId}`,
            tableId: 1
        };

        const response = await axios.post(`${API_BASE_URL}/api/payments/momo/create`, paymentData);
        
        if (response.data.success) {
            log('✅ Tạo thanh toán MoMo thành công!', 'green');
            log(`🔗 URL thanh toán: ${response.data.payUrl}`, 'cyan');
            return response.data;
        } else {
            throw new Error(response.data.message || 'Lỗi tạo thanh toán MoMo');
        }
    } catch (error) {
        throw new Error(`Lỗi test MoMo: ${error.response?.data?.message || error.message}`);
    }
}

// Test thanh toán tiền mặt
async function testCashPayment(orderId, amount, token) {
    try {
        const paymentData = {
            orderId: orderId,
            amount: amount,
            cashReceived: amount + 50000 // Thêm 50k tiền thừa
        };

        const response = await axios.post(`${API_BASE_URL}/api/payments/cash`, paymentData, {
            headers: { 'Authorization': `Bearer ${token}` }
        });
        
        if (response.data.success) {
            log('✅ Thanh toán tiền mặt thành công!', 'green');
            log(`💰 Tiền thừa: ${response.data.cashChange.toLocaleString('vi-VN')} VNĐ`, 'cyan');
            return response.data;
        } else {
            throw new Error(response.data.message || 'Lỗi thanh toán tiền mặt');
        }
    } catch (error) {
        throw new Error(`Lỗi test tiền mặt: ${error.response?.data?.message || error.message}`);
    }
}

// Kiểm tra trạng thái thanh toán
async function checkPaymentStatus(orderId) {
    try {
        const response = await axios.get(`${API_BASE_URL}/api/payments/status/${orderId}`);
        log('📊 Trạng thái thanh toán:', 'blue');
        console.log(JSON.stringify(response.data, null, 2));
        return response.data;
    } catch (error) {
        throw new Error(`Lỗi kiểm tra trạng thái: ${error.response?.data?.message || error.message}`);
    }
}

// Test flow đầy đủ
async function runFullPaymentFlow() {
    try {
        log('🚀 Bắt đầu test flow thanh toán đầy đủ...', 'bright');
        log('=' .repeat(60), 'cyan');

        // 1. Đăng nhập admin
        log('\n1️⃣ Đăng nhập admin...', 'yellow');
        const token = await loginAdmin();
        log('✅ Đăng nhập thành công!', 'green');

        // 2. Tạo QR key cho bàn 1
        log('\n2️⃣ Tạo QR key cho bàn 1...', 'yellow');
        const qrData = await createTableKey(1, token);
        log(`✅ Tạo QR key thành công! Key: ${qrData.key}`, 'green');

        // 3. Tạo đơn hàng từ khách hàng
        log('\n3️⃣ Tạo đơn hàng từ khách hàng...', 'yellow');
        const order = await createCustomerOrder(1, qrData.key);
        log(`✅ Tạo đơn hàng thành công! ID: ${order.id}, Tổng: ${order.total.toLocaleString('vi-VN')} VNĐ`, 'green');

        // 4. Cập nhật trạng thái đơn hàng thành "Hoàn thành"
        log('\n4️⃣ Cập nhật trạng thái đơn hàng...', 'yellow');
        await completeOrder(order.id, token);

        // 5. Test thanh toán MoMo
        log('\n5️⃣ Test thanh toán MoMo...', 'yellow');
        const momoResult = await testMoMoPayment(order.id, order.total);

        // 6. Hiển thị hướng dẫn test MoMo
        log('\n📱 HƯỚNG DẪN TEST MOMO SANDBOX:', 'magenta');
        log('=' .repeat(50), 'cyan');
        log('🔗 Mở URL thanh toán ở trên trong trình duyệt', 'cyan');
        log('📱 Hoặc quét QR code bằng ứng dụng MoMo', 'cyan');
        log('💡 Sử dụng tài khoản MoMo test bất kỳ', 'cyan');
        log('🔐 Nhập OTP test: 123456', 'cyan');
        log('=' .repeat(50), 'cyan');

        // 7. Kiểm tra trạng thái ban đầu
        log('\n6️⃣ Kiểm tra trạng thái thanh toán ban đầu...', 'yellow');
        await checkPaymentStatus(order.id);

        // 8. Hướng dẫn tiếp theo
        log('\n🎯 BƯỚC TIẾP THEO:', 'bright');
        log('1. Mở URL MoMo ở trên để test thanh toán', 'cyan');
        log('2. Hoặc chạy: node test_cash_payment.js để test thanh toán tiền mặt', 'cyan');
        log(`3. Hoặc truy cập: ${TABLE_SERVICE_URL}/order?table_id=1&key=${qrData.key}`, 'cyan');

        log('\n✅ Test flow hoàn thành!', 'green');
        
        return {
            orderId: order.id,
            amount: order.total,
            tableKey: qrData.key,
            momoUrl: momoResult.payUrl
        };

    } catch (error) {
        log(`💥 Lỗi trong quá trình test: ${error.message}`, 'red');
        throw error;
    }
}

// Test riêng thanh toán tiền mặt
async function runCashPaymentTest() {
    try {
        log('💵 Test thanh toán tiền mặt...', 'bright');
        
        const token = await loginAdmin();
        
        // Lấy đơn hàng gần nhất chưa thanh toán
        const ordersResponse = await axios.get(`${API_BASE_URL}/api/orders`, {
            headers: { 'Authorization': `Bearer ${token}` }
        });
        
        const unpaidOrder = ordersResponse.data.find(order => order.payment_status === 'unpaid');
        
        if (!unpaidOrder) {
            log('❌ Không tìm thấy đơn hàng chưa thanh toán. Chạy test flow đầy đủ trước.', 'red');
            return;
        }

        log(`📋 Tìm thấy đơn hàng #${unpaidOrder.id} - ${unpaidOrder.total.toLocaleString('vi-VN')} VNĐ`, 'cyan');
        
        await testCashPayment(unpaidOrder.id, unpaidOrder.total, token);
        await checkPaymentStatus(unpaidOrder.id);
        
    } catch (error) {
        log(`💥 Lỗi test tiền mặt: ${error.message}`, 'red');
    }
}

// Main function
async function main() {
    const args = process.argv.slice(2);
    
    if (args.includes('--cash')) {
        await runCashPaymentTest();
    } else {
        await runFullPaymentFlow();
    }
}

// Chạy nếu file được gọi trực tiếp
if (require.main === module) {
    main().catch(error => {
        log(`💥 Lỗi: ${error.message}`, 'red');
        process.exit(1);
    });
}

module.exports = {
    runFullPaymentFlow,
    runCashPaymentTest,
    testMoMoPayment,
    testCashPayment
};
